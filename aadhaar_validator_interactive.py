#!/usr/bin/env python3
"""
Interactive Aadhaar Validator - Step by step with user confirmation
"""

import time
import base64
import io
import tkinter as tk
from tkinter import messagebox, simpledialog
from PIL import Image, ImageTk
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class InteractiveAadhaarValidator:
    def __init__(self):
        self.driver = None
        self.target_url = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
        
    def setup_driver(self):
        """Setup WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            logger.info("WebDriver initialized")
            return True
        except Exception as e:
            logger.error(f"WebDriver setup failed: {e}")
            return False
    
    def step1_load_website(self):
        """Step 1: Load website"""
        try:
            logger.info("Step 1: Loading UIDAI website...")
            self.driver.get(self.target_url)
            time.sleep(5)
            
            logger.info(f"Website loaded: {self.driver.title}")
            
            # Show confirmation dialog
            root = tk.Tk()
            root.withdraw()
            result = messagebox.askyesno("Step 1 Complete", 
                f"✅ Website loaded successfully!\n\nTitle: {self.driver.title}\n\nProceed to Step 2?")
            root.destroy()
            
            return result
        except Exception as e:
            logger.error(f"Step 1 failed: {e}")
            return False
    
    def step2_get_aadhaar(self):
        """Step 2: Get Aadhaar number"""
        try:
            logger.info("Step 2: Getting Aadhaar number...")
            
            root = tk.Tk()
            root.withdraw()
            
            aadhaar = simpledialog.askstring(
                "Step 2: Enter Aadhaar", 
                "Website is loaded! Enter your 12-digit Aadhaar number:",
                parent=root
            )
            root.destroy()
            
            if not aadhaar:
                return None
                
            aadhaar = aadhaar.replace(" ", "").replace("-", "")
            if len(aadhaar) == 12 and aadhaar.isdigit():
                logger.info(f"Got Aadhaar: {aadhaar}")
                return aadhaar
            else:
                messagebox.showerror("Invalid", "Please enter a valid 12-digit Aadhaar number")
                return None
                
        except Exception as e:
            logger.error(f"Step 2 failed: {e}")
            return None
    
    def step3_fill_aadhaar(self, aadhaar):
        """Step 3: Fill Aadhaar number"""
        try:
            logger.info("Step 3: Filling Aadhaar number...")
            
            # Find input field
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            logger.info(f"Found {len(inputs)} input elements")
            
            target_input = None
            for i, inp in enumerate(inputs):
                try:
                    input_type = inp.get_attribute("type") or ""
                    is_displayed = inp.is_displayed()
                    is_enabled = inp.is_enabled()
                    
                    if is_displayed and is_enabled and input_type == "text":
                        target_input = inp
                        logger.info(f"Selected input {i+1} as target")
                        break
                except:
                    continue
            
            if not target_input:
                logger.error("No suitable input field found")
                return False
            
            # Fill using JavaScript
            self.driver.execute_script("arguments[0].value = arguments[1];", target_input, aadhaar)
            
            # Verify
            current_value = target_input.get_attribute("value")
            if current_value == aadhaar:
                logger.info("✅ Aadhaar filled successfully")
                
                # Show confirmation
                root = tk.Tk()
                root.withdraw()
                result = messagebox.askyesno("Step 3 Complete", 
                    f"✅ Aadhaar number filled successfully!\n\nValue: {current_value}\n\nProceed to Step 4?")
                root.destroy()
                
                return result
            else:
                logger.error(f"Fill failed. Expected: {aadhaar}, Got: {current_value}")
                return False
                
        except Exception as e:
            logger.error(f"Step 3 failed: {e}")
            return False
    
    def step4_wait_and_inspect(self):
        """Step 4: Wait for captcha and inspect page"""
        try:
            logger.info("Step 4: Waiting for captcha to appear...")
            
            # Wait for dynamic content
            time.sleep(3)
            
            # Inspect page
            images = self.driver.find_elements(By.TAG_NAME, "img")
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            
            logger.info(f"After waiting: {len(images)} images, {len(inputs)} inputs, {len(buttons)} buttons")
            
            # Look for captcha images
            captcha_found = False
            for img in images:
                try:
                    src = img.get_attribute("src") or ""
                    if "data:image" in src and img.is_displayed():
                        logger.info("🎯 Found potential captcha image!")
                        captcha_found = True
                        break
                except:
                    continue
            
            # Show inspection dialog
            root = tk.Tk()
            root.withdraw()
            
            message = f"""Step 4: Page Inspection Results

🔍 Elements found:
• Images: {len(images)}
• Input fields: {len(inputs)} 
• Buttons: {len(buttons)}

🎯 Captcha status: {"Found!" if captcha_found else "Not found"}

The browser window is open for you to inspect manually.
Look for:
- Captcha image that appeared
- Captcha input field
- Submit/Proceed button

Continue to captcha extraction?"""
            
            result = messagebox.askyesno("Step 4: Inspection", message)
            root.destroy()
            
            return result
            
        except Exception as e:
            logger.error(f"Step 4 failed: {e}")
            return False
    
    def step5_extract_captcha(self):
        """Step 5: Extract captcha image"""
        try:
            logger.info("Step 5: Extracting captcha image...")
            
            # Look for images with base64 data
            images = self.driver.find_elements(By.TAG_NAME, "img")
            
            for i, img in enumerate(images):
                try:
                    src = img.get_attribute("src") or ""
                    if "data:image" in src and img.is_displayed():
                        logger.info(f"Found base64 image {i+1}")
                        
                        # Extract and convert
                        base64_data = src.split(",")[1]
                        image_data = base64.b64decode(base64_data)
                        image = Image.open(io.BytesIO(image_data))
                        
                        logger.info("✅ Captcha image extracted successfully")
                        return image
                        
                except Exception as e:
                    logger.warning(f"Error processing image {i+1}: {e}")
                    continue
            
            logger.error("No captcha image found")
            return None
            
        except Exception as e:
            logger.error(f"Step 5 failed: {e}")
            return None
    
    def step6_solve_captcha(self, captcha_image):
        """Step 6: Show captcha GUI and get solution"""
        try:
            logger.info("Step 6: Showing captcha GUI...")
            
            root = tk.Tk()
            root.title("Step 6: Solve Captcha")
            root.geometry("400x300")
            
            # Display image
            photo = ImageTk.PhotoImage(captcha_image.resize((200, 100)))
            img_label = tk.Label(root, image=photo)
            img_label.pack(pady=10)
            
            tk.Label(root, text="Enter the captcha text:", font=("Arial", 12)).pack(pady=5)
            
            captcha_entry = tk.Entry(root, font=("Arial", 14), width=15)
            captcha_entry.pack(pady=5)
            captcha_entry.focus()
            
            captcha_text = None
            
            def submit():
                nonlocal captcha_text
                text = captcha_entry.get().strip()
                if text:
                    captcha_text = text
                    root.destroy()
                else:
                    messagebox.showerror("Error", "Please enter the captcha text")
            
            tk.Button(root, text="Submit", command=submit, 
                     font=("Arial", 12), bg="#28A745", fg="white").pack(pady=10)
            
            root.mainloop()
            
            if captcha_text:
                logger.info(f"✅ Got captcha solution: {captcha_text}")
            
            return captcha_text
            
        except Exception as e:
            logger.error(f"Step 6 failed: {e}")
            return None
    
    def run_interactive(self):
        """Run interactive validation"""
        try:
            logger.info("🚀 Starting Interactive Aadhaar Validator")
            
            # Setup
            if not self.setup_driver():
                messagebox.showerror("Error", "Failed to setup browser")
                return
            
            # Step 1: Load website
            if not self.step1_load_website():
                return
            
            # Step 2: Get Aadhaar
            aadhaar = self.step2_get_aadhaar()
            if not aadhaar:
                return
            
            # Step 3: Fill Aadhaar
            if not self.step3_fill_aadhaar(aadhaar):
                return
            
            # Step 4: Wait and inspect
            if not self.step4_wait_and_inspect():
                return
            
            # Step 5: Extract captcha
            captcha_image = self.step5_extract_captcha()
            if not captcha_image:
                messagebox.showwarning("Warning", 
                    "Could not extract captcha automatically.\n"
                    "Please complete the process manually in the browser.")
                input("Press Enter to close browser...")
                return
            
            # Step 6: Solve captcha
            captcha_text = self.step6_solve_captcha(captcha_image)
            if not captcha_text:
                return
            
            messagebox.showinfo("Success", 
                f"✅ Process completed!\n\n"
                f"Aadhaar: {aadhaar}\n"
                f"Captcha: {captcha_text}\n\n"
                f"Now you need to:\n"
                f"1. Fill the captcha in the browser\n"
                f"2. Click the Proceed button\n"
                f"3. View the results")
            
            input("Press Enter to close browser...")
            
        except Exception as e:
            logger.error(f"Interactive validation failed: {e}")
            messagebox.showerror("Error", f"An error occurred: {e}")
        finally:
            if self.driver:
                self.driver.quit()

if __name__ == "__main__":
    validator = InteractiveAadhaarValidator()
    validator.run_interactive()
