#!/usr/bin/env python3
"""
Quick test to see if cap<PERSON><PERSON> appears after filling <PERSON><PERSON><PERSON><PERSON>
"""

import time
import tkinter as tk
from tkinter import simpledialog, messagebox
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def quick_captcha_test():
    """Quick test for captcha appearance"""
    
    print("🧪 Quick Captcha Test")
    
    # Setup
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        # Load website
        print("Loading website...")
        driver.get("https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en")
        time.sleep(5)
        
        # Get Aadhaar
        root = tk.Tk()
        root.withdraw()
        aadhaar = simpledialog.askstring("Test", "Enter Aadhaar number:", parent=root)
        root.destroy()
        
        if not aadhaar:
            return
        
        # Fill Aadhaar
        print("Filling Aadhaar...")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        for inp in inputs:
            if inp.is_displayed() and inp.is_enabled():
                driver.execute_script("arguments[0].value = arguments[1];", inp, aadhaar)
                print(f"✅ Filled Aadhaar: {inp.get_attribute('value')}")
                break
        
        # Wait and check for captcha
        print("Waiting 5 seconds for captcha...")
        time.sleep(5)
        
        # Check images
        images = driver.find_elements(By.TAG_NAME, "img")
        print(f"Found {len(images)} images after filling Aadhaar:")
        
        captcha_found = False
        for i, img in enumerate(images):
            try:
                src = img.get_attribute("src") or ""
                img_class = img.get_attribute("class") or ""
                is_displayed = img.is_displayed()
                
                print(f"Image {i+1}: displayed={is_displayed}, class='{img_class}'")
                
                if "data:image" in src and is_displayed:
                    print(f"  🎯 BASE64 IMAGE FOUND! Length: {len(src)}")
                    captcha_found = True
                elif src:
                    print(f"  URL: {src[:50]}...")
                else:
                    print(f"  No src attribute")
                    
            except Exception as e:
                print(f"  Error: {e}")
        
        # Check inputs again
        inputs_after = driver.find_elements(By.TAG_NAME, "input")
        print(f"\nInput fields after Aadhaar: {len(inputs_after)}")
        
        for i, inp in enumerate(inputs_after):
            try:
                input_type = inp.get_attribute("type") or ""
                input_class = inp.get_attribute("class") or ""
                input_value = inp.get_attribute("value") or ""
                is_displayed = inp.is_displayed()
                
                print(f"Input {i+1}: type={input_type}, class='{input_class[:30]}', value='{input_value[:10]}', displayed={is_displayed}")
                
            except Exception as e:
                print(f"  Error: {e}")
        
        # Check buttons
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"\nButtons found: {len(buttons)}")
        
        for i, btn in enumerate(buttons):
            try:
                btn_text = btn.text or ""
                btn_class = btn.get_attribute("class") or ""
                is_displayed = btn.is_displayed()
                
                print(f"Button {i+1}: text='{btn_text}', class='{btn_class[:30]}', displayed={is_displayed}")
                
            except Exception as e:
                print(f"  Error: {e}")
        
        # Summary
        print(f"\n📊 SUMMARY:")
        print(f"Captcha found: {'YES' if captcha_found else 'NO'}")
        print(f"Total images: {len(images)}")
        print(f"Total inputs: {len(inputs_after)}")
        print(f"Total buttons: {len(buttons)}")
        
        # Ask user what they see
        root = tk.Tk()
        root.withdraw()
        user_sees_captcha = messagebox.askyesno("Manual Check", 
            f"Automated check: {'Found' if captcha_found else 'Not found'} captcha\n\n"
            f"Please look at the browser window:\n"
            f"Do you see a captcha image displayed?")
        root.destroy()
        
        if user_sees_captcha:
            print("✅ User confirms captcha is visible")
        else:
            print("❌ User says no captcha visible")
        
        input("Press Enter to close...")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    quick_captcha_test()
