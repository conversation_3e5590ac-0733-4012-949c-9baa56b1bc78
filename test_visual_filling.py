#!/usr/bin/env python3
"""
Visual Test - Check if filling is actually working in browser
"""

import time
import tkinter as tk
from tkinter import messagebox, simpledialog
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def test_visual_filling():
    """Test filling with visual confirmation"""
    
    print("🧪 Visual Filling Test")
    
    # Setup
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        # Load website
        print("Loading website...")
        driver.get("https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en")
        time.sleep(5)
        
        # Get Aadhaar
        root = tk.Tk()
        root.withdraw()
        aadhaar = simpledialog.askstring("Test", "Enter Aadhaar number:", parent=root)
        root.destroy()
        
        if not aadhaar:
            return
        
        print(f"Testing with Aadhaar: {aadhaar}")
        
        # Find inputs
        inputs = driver.find_elements(By.CSS_SELECTOR, "input.sc-fBWQRz.bjSLyk")
        print(f"Found {len(inputs)} input fields")
        
        if len(inputs) < 1:
            print("❌ No input fields found")
            return
        
        aadhaar_input = inputs[0]
        
        # Test multiple filling methods
        methods = [
            ("JavaScript with Events", fill_with_events),
            ("Clear and Type", fill_clear_and_type),
            ("Focus and Type", fill_focus_and_type),
            ("Click and Type", fill_click_and_type)
        ]
        
        for method_name, method_func in methods:
            print(f"\n🧪 Testing: {method_name}")
            
            try:
                # Clear field first
                driver.execute_script("arguments[0].value = '';", aadhaar_input)
                time.sleep(1)
                
                # Apply method
                method_func(driver, aadhaar_input, aadhaar)
                time.sleep(2)
                
                # Check result
                value_attr = aadhaar_input.get_attribute("value")
                value_js = driver.execute_script("return arguments[0].value;", aadhaar_input)
                
                print(f"  Attribute value: {value_attr}")
                print(f"  JavaScript value: {value_js}")
                
                # Ask user what they see
                root = tk.Tk()
                root.withdraw()
                user_sees = messagebox.askyesno("Visual Check", 
                    f"Method: {method_name}\n\n"
                    f"Attribute shows: {value_attr}\n"
                    f"JavaScript shows: {value_js}\n\n"
                    f"Do you see '{aadhaar}' filled in the Aadhaar field in the browser?")
                root.destroy()
                
                if user_sees:
                    print(f"  ✅ {method_name} WORKS - User confirms visual filling")
                    
                    # Test captcha filling if this method works
                    if len(inputs) > 1:
                        print(f"  Testing captcha filling with {method_name}...")
                        
                        root = tk.Tk()
                        root.withdraw()
                        captcha_test = simpledialog.askstring("Captcha Test", 
                            "Enter test captcha text:", parent=root)
                        root.destroy()
                        
                        if captcha_test:
                            captcha_input = inputs[1]
                            driver.execute_script("arguments[0].value = '';", captcha_input)
                            time.sleep(1)
                            
                            method_func(driver, captcha_input, captcha_test)
                            time.sleep(2)
                            
                            captcha_value = captcha_input.get_attribute("value")
                            print(f"  Captcha filled: {captcha_value}")
                            
                            root = tk.Tk()
                            root.withdraw()
                            captcha_works = messagebox.askyesno("Captcha Check", 
                                f"Do you see '{captcha_test}' in the captcha field?")
                            root.destroy()
                            
                            if captcha_works:
                                print(f"  ✅ Captcha filling also works with {method_name}")
                                
                                # Save the working method
                                with open("working_method.txt", "w") as f:
                                    f.write(f"Working method: {method_name}\n")
                                    f.write(f"Aadhaar test: {aadhaar} -> {value_attr}\n")
                                    f.write(f"Captcha test: {captcha_test} -> {captcha_value}\n")
                                
                                print(f"✅ FOUND WORKING METHOD: {method_name}")
                                break
                            else:
                                print(f"  ❌ Captcha filling failed with {method_name}")
                    
                else:
                    print(f"  ❌ {method_name} FAILED - User doesn't see filling")
                    
            except Exception as e:
                print(f"  ❌ {method_name} ERROR: {e}")
        
        input("\nPress Enter to close browser...")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        driver.quit()

def fill_with_events(driver, element, text):
    """Fill with JavaScript events"""
    script = """
    var element = arguments[0];
    var text = arguments[1];
    
    element.focus();
    element.value = '';
    element.value = text;
    
    var events = ['input', 'change', 'keyup', 'keydown', 'blur', 'focus'];
    events.forEach(function(eventType) {
        var event = new Event(eventType, { bubbles: true });
        element.dispatchEvent(event);
    });
    
    // React synthetic events
    var inputEvent = new Event('input', { bubbles: true });
    inputEvent.simulated = true;
    element.dispatchEvent(inputEvent);
    """
    driver.execute_script(script, element, text)

def fill_clear_and_type(driver, element, text):
    """Fill by clearing and typing"""
    element.click()
    time.sleep(0.5)
    element.clear()
    time.sleep(0.5)
    
    for char in text:
        element.send_keys(char)
        time.sleep(0.1)

def fill_focus_and_type(driver, element, text):
    """Fill by focusing and typing"""
    driver.execute_script("arguments[0].focus();", element)
    time.sleep(0.5)
    element.send_keys(Keys.CONTROL + "a")
    time.sleep(0.2)
    element.send_keys(Keys.DELETE)
    time.sleep(0.5)
    element.send_keys(text)

def fill_click_and_type(driver, element, text):
    """Fill by clicking and typing"""
    element.click()
    time.sleep(0.5)
    element.send_keys(Keys.CONTROL + "a")
    time.sleep(0.2)
    element.send_keys(text)

if __name__ == "__main__":
    test_visual_filling()
