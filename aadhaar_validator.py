#!/usr/bin/env python3
"""
High-Performance Aadhaar Validation Automation Script
Target: https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en
"""

import time
import base64
import io
import tkinter as tk
from tkinter import messagebox, simpledialog
from PIL import Image, ImageTk
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AadhaarValidator:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.target_url = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
        
    def setup_driver(self):
        """Initialize Chrome WebDriver with optimized settings"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # Use webdriver-manager to handle ChromeDriver installation
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 10)
            logger.info("WebDriver initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize WebDriver: {e}")
            return False
    
    def get_aadhaar_number(self):
        """Get Aadhaar number from user input"""
        root = tk.Tk()
        root.withdraw()  # Hide the main window
        
        while True:
            aadhaar = simpledialog.askstring(
                "Aadhaar Validation", 
                "Enter your 12-digit Aadhaar number:",
                parent=root
            )
            
            if aadhaar is None:  # User cancelled
                root.destroy()
                return None
                
            # Validate Aadhaar number format
            aadhaar = aadhaar.replace(" ", "").replace("-", "")
            if len(aadhaar) == 12 and aadhaar.isdigit():
                root.destroy()
                return aadhaar
            else:
                messagebox.showerror("Invalid Input", "Please enter a valid 12-digit Aadhaar number")
        
    def navigate_to_website(self):
        """Navigate to the Aadhaar validation website"""
        try:
            logger.info(f"Navigating to {self.target_url}")
            self.driver.get(self.target_url)
            
            # Wait for the form to load
            self.wait.until(EC.presence_of_element_located((By.CLASS_NAME, "auth-form__text-field")))
            logger.info("Website loaded successfully")
            return True
        except TimeoutException:
            logger.error("Timeout waiting for website to load")
            return False
        except Exception as e:
            logger.error(f"Error navigating to website: {e}")
            return False
    
    def fill_aadhaar_number(self, aadhaar_number):
        """Fill the Aadhaar number in the form"""
        try:
            aadhaar_field = self.wait.until(
                EC.element_to_be_clickable((By.CLASS_NAME, "auth-form__text-field"))
            )
            aadhaar_field.clear()
            aadhaar_field.send_keys(aadhaar_number)
            logger.info("Aadhaar number filled successfully")
            return True
        except Exception as e:
            logger.error(f"Error filling Aadhaar number: {e}")
            return False
    
    def extract_captcha_image(self):
        """Extract captcha image from the page"""
        try:
            captcha_element = self.wait.until(
                EC.presence_of_element_located((By.CLASS_NAME, "auth-form__captcha-box"))
            )
            
            # Find the img element within the captcha box
            img_element = captcha_element.find_element(By.TAG_NAME, "img")
            img_src = img_element.get_attribute("src")
            
            if img_src.startswith("data:image"):
                # Extract base64 data
                base64_data = img_src.split(",")[1]
                image_data = base64.b64decode(base64_data)
                image = Image.open(io.BytesIO(image_data))
                logger.info("Captcha image extracted successfully")
                return image
            else:
                logger.error("Captcha image is not in base64 format")
                return None
                
        except Exception as e:
            logger.error(f"Error extracting captcha image: {e}")
            return None
    
    def solve_captcha_gui(self, captcha_image):
        """Display captcha in GUI and get user input"""
        if captcha_image is None:
            return None
            
        root = tk.Tk()
        root.title("Captcha Solver")
        root.geometry("400x300")
        
        # Display captcha image
        photo = ImageTk.PhotoImage(captcha_image.resize((200, 100)))
        img_label = tk.Label(root, image=photo)
        img_label.pack(pady=10)
        
        # Input field for captcha
        tk.Label(root, text="Enter the captcha text:", font=("Arial", 12)).pack(pady=5)
        captcha_entry = tk.Entry(root, font=("Arial", 14), width=20)
        captcha_entry.pack(pady=5)
        captcha_entry.focus()
        
        captcha_text = None
        
        def submit_captcha():
            nonlocal captcha_text
            captcha_text = captcha_entry.get().strip()
            if captcha_text:
                root.destroy()
            else:
                messagebox.showerror("Error", "Please enter the captcha text")
        
        def on_enter(event):
            submit_captcha()
        
        captcha_entry.bind('<Return>', on_enter)
        
        submit_btn = tk.Button(root, text="Submit", command=submit_captcha, 
                              font=("Arial", 12), bg="#4CAF50", fg="white")
        submit_btn.pack(pady=10)
        
        root.mainloop()
        return captcha_text
    
    def fill_captcha(self, captcha_text):
        """Fill the captcha in the form"""
        try:
            captcha_field = self.wait.until(
                EC.element_to_be_clickable((By.CLASS_NAME, "auth-form__captcha-field"))
            )
            captcha_field.clear()
            captcha_field.send_keys(captcha_text)
            logger.info("Captcha filled successfully")
            return True
        except Exception as e:
            logger.error(f"Error filling captcha: {e}")
            return False
    
    def submit_form(self):
        """Submit the form by clicking the Proceed button"""
        try:
            proceed_button = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, ".auth-form__button-container .auth-form__button"))
            )
            proceed_button.click()
            logger.info("Form submitted successfully")
            
            # Wait a moment for the page to process
            time.sleep(3)
            return True
        except Exception as e:
            logger.error(f"Error submitting form: {e}")
            return False
    
    def check_submission_success(self):
        """Check if form submission was successful by looking for form elements"""
        try:
            # Try to find the form elements - if they exist, we're still on the form page
            self.driver.find_element(By.CLASS_NAME, "auth-form__text-field")
            self.driver.find_element(By.CLASS_NAME, "auth-form__captcha-field")
            logger.info("Form elements still present - submission may have failed")
            return False
        except NoSuchElementException:
            logger.info("Form elements not found - moved to results page")
            return True
        except Exception as e:
            logger.error(f"Error checking submission success: {e}")
            return False
    
    def extract_validation_results(self):
        """Extract validation results from the results page"""
        try:
            # Wait for results to load
            time.sleep(2)
            
            results = {}
            page_source = self.driver.page_source.lower()
            
            # Check if Aadhaar exists
            if "exists" in page_source or "valid" in page_source:
                results["status"] = "EXISTS"
            else:
                results["status"] = "NOT_EXISTS"
            
            # Try to extract additional information
            try:
                # Look for common patterns in the results
                if "age band" in page_source:
                    # Extract age band, gender, state, mobile info
                    # This would need to be customized based on actual page structure
                    results["details"] = "Additional details found on page"
                
                logger.info("Validation results extracted successfully")
            except Exception as e:
                logger.warning(f"Could not extract detailed results: {e}")
            
            return results
        except Exception as e:
            logger.error(f"Error extracting validation results: {e}")
            return None
    
    def display_results(self, results):
        """Display validation results to user"""
        if results is None:
            messagebox.showerror("Error", "Could not retrieve validation results")
            return
        
        root = tk.Tk()
        root.title("Aadhaar Validation Results")
        root.geometry("400x300")
        
        tk.Label(root, text="Aadhaar Validation Results", 
                font=("Arial", 16, "bold")).pack(pady=10)
        
        status_text = f"Status: {results.get('status', 'Unknown')}"
        status_color = "green" if results.get('status') == 'EXISTS' else "red"
        
        tk.Label(root, text=status_text, font=("Arial", 14), 
                fg=status_color).pack(pady=5)
        
        if "details" in results:
            tk.Label(root, text=results["details"], 
                    font=("Arial", 12)).pack(pady=5)
        
        tk.Button(root, text="Close", command=root.destroy,
                 font=("Arial", 12), bg="#f44336", fg="white").pack(pady=20)
        
        root.mainloop()
    
    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            self.driver.quit()
            logger.info("WebDriver closed")
    
    def run(self):
        """Main execution flow"""
        try:
            logger.info("Starting Aadhaar validation automation")
            
            # Setup WebDriver
            if not self.setup_driver():
                return False
            
            # Get Aadhaar number from user
            aadhaar_number = self.get_aadhaar_number()
            if not aadhaar_number:
                logger.info("User cancelled Aadhaar input")
                return False
            
            # Navigate to website
            if not self.navigate_to_website():
                return False
            
            # Fill Aadhaar number
            if not self.fill_aadhaar_number(aadhaar_number):
                return False
            
            # Extract and solve captcha
            captcha_image = self.extract_captcha_image()
            captcha_text = self.solve_captcha_gui(captcha_image)
            
            if not captcha_text:
                logger.info("User cancelled captcha input")
                return False
            
            # Fill captcha
            if not self.fill_captcha(captcha_text):
                return False
            
            # Submit form
            if not self.submit_form():
                return False
            
            # Check if submission was successful
            if not self.check_submission_success():
                messagebox.showerror("Error", "Form submission failed. Please try again.")
                return False
            
            # Extract and display results
            results = self.extract_validation_results()
            self.display_results(results)
            
            logger.info("Aadhaar validation completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Unexpected error in main execution: {e}")
            messagebox.showerror("Error", f"An unexpected error occurred: {e}")
            return False
        finally:
            self.cleanup()

if __name__ == "__main__":
    validator = AadhaarValidator()
    validator.run()
