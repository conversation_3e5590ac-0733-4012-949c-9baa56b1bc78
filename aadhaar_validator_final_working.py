#!/usr/bin/env python3
"""
FINAL WORKING Aadhaar Validator - Extracts from exact HTML structure
Uses the discovered card structure: check-aadhaar-validity-response__card
"""

import time
import base64
import io
import tkinter as tk
from tkinter import messagebox, simpledialog
from PIL import Image, ImageTk
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalWorkingAadhaarValidator:
    def __init__(self):
        self.driver = None
        self.target_url = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
        
    def setup_driver(self):
        """Setup WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            logger.info("WebDriver initialized")
            return True
        except Exception as e:
            logger.error(f"WebDriver setup failed: {e}")
            return False
    
    def step1_load_website(self):
        """Step 1: Load website FIRST"""
        try:
            logger.info("Step 1: Loading UIDAI website...")
            self.driver.get(self.target_url)
            time.sleep(5)
            logger.info(f"✅ Website loaded: {self.driver.title}")
            return True
        except Exception as e:
            logger.error(f"Step 1 failed: {e}")
            return False
    
    def step2_get_aadhaar(self):
        """Step 2: Get Aadhaar number AFTER website loads"""
        try:
            logger.info("Step 2: Getting Aadhaar number...")
            
            root = tk.Tk()
            root.withdraw()
            
            aadhaar = simpledialog.askstring(
                "Step 2: Aadhaar Input", 
                "✅ Website loaded successfully!\n\nEnter your 12-digit Aadhaar number:",
                parent=root
            )
            root.destroy()
            
            if not aadhaar:
                return None
                
            aadhaar = aadhaar.replace(" ", "").replace("-", "")
            if len(aadhaar) == 12 and aadhaar.isdigit():
                logger.info(f"✅ Got Aadhaar: {aadhaar}")
                return aadhaar
            else:
                messagebox.showerror("Invalid", "Please enter a valid 12-digit Aadhaar number")
                return None
                
        except Exception as e:
            logger.error(f"Step 2 failed: {e}")
            return None
    
    def step3_fill_aadhaar(self, aadhaar):
        """Step 3: Fill Aadhaar in first input field"""
        try:
            logger.info("Step 3: Filling Aadhaar number...")
            
            inputs = self.driver.find_elements(By.CSS_SELECTOR, "input.sc-fBWQRz.bjSLyk")
            logger.info(f"Found {len(inputs)} input elements")
            
            if len(inputs) < 1:
                logger.error("Could not find Aadhaar input field")
                return False
            
            # First input is for Aadhaar
            aadhaar_input = inputs[0]
            self.driver.execute_script("arguments[0].value = arguments[1];", aadhaar_input, aadhaar)
            
            # Verify
            current_value = aadhaar_input.get_attribute("value")
            if current_value == aadhaar:
                logger.info("✅ Aadhaar number filled successfully")
                return True
            else:
                logger.error(f"Fill failed. Expected: {aadhaar}, Got: {current_value}")
                return False
                
        except Exception as e:
            logger.error(f"Step 3 failed: {e}")
            return False
    
    def step4_extract_captcha(self):
        """Step 4: Extract captcha using exact class"""
        try:
            logger.info("Step 4: Extracting captcha image...")
            time.sleep(2)
            
            captcha_img = self.driver.find_element(By.CSS_SELECTOR, 
                "img.auth-form__captcha-image.check-aadhaar-validity__captcha-image")
            
            if captcha_img.is_displayed():
                src = captcha_img.get_attribute("src")
                logger.info("Found captcha image")
                
                if "data:" in src and ("image" in src or "application/image" in src):
                    base64_data = src.split(",")[1]
                    image_data = base64.b64decode(base64_data)
                    image = Image.open(io.BytesIO(image_data))
                    logger.info("✅ Successfully extracted captcha image")
                    return image
                        
            logger.error("Could not find captcha image")
            return None
            
        except Exception as e:
            logger.error(f"Step 4 failed: {e}")
            return None
    
    def step5_show_captcha_gui(self, captcha_image):
        """Step 5: Show captcha in GUI"""
        try:
            logger.info("Step 5: Showing captcha GUI...")
            
            root = tk.Tk()
            root.title("🔐 Solve Captcha")
            root.geometry("500x400")
            root.eval('tk::PlaceWindow . center')
            
            tk.Label(root, text="🔐 Step 5: Solve Captcha", 
                    font=("Arial", 16, "bold"), fg="#2E86AB").pack(pady=10)
            
            display_image = captcha_image.resize((300, 150), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(display_image)
            
            img_frame = tk.Frame(root, relief="solid", bd=2)
            img_frame.pack(pady=10)
            img_label = tk.Label(img_frame, image=photo, bg="white")
            img_label.pack(padx=10, pady=10)
            
            tk.Label(root, text="Enter the captcha text:", font=("Arial", 12)).pack(pady=5)
            
            captcha_entry = tk.Entry(root, font=("Arial", 16), width=15, justify="center")
            captcha_entry.pack(pady=5)
            captcha_entry.focus()
            
            captcha_text = None
            
            def submit():
                nonlocal captcha_text
                text = captcha_entry.get().strip()
                if text:
                    captcha_text = text
                    root.destroy()
                else:
                    messagebox.showerror("Error", "Please enter the captcha text")
            
            captcha_entry.bind('<Return>', lambda e: submit())
            
            button_frame = tk.Frame(root)
            button_frame.pack(pady=20)
            
            tk.Button(button_frame, text="✓ Submit", command=submit,
                     font=("Arial", 12, "bold"), bg="#28A745", fg="white",
                     padx=20, pady=5).pack(side=tk.LEFT, padx=10)
            
            tk.Button(button_frame, text="✗ Cancel", command=root.destroy,
                     font=("Arial", 12), bg="#DC3545", fg="white",
                     padx=20, pady=5).pack(side=tk.LEFT, padx=10)
            
            root.mainloop()
            
            if captcha_text:
                logger.info(f"✅ Got captcha solution: {captcha_text}")
            
            return captcha_text
            
        except Exception as e:
            logger.error(f"Step 5 failed: {e}")
            return None
    
    def step6_fill_captcha(self, captcha_text):
        """Step 6: Fill captcha in second input field"""
        try:
            logger.info("Step 6: Filling captcha in website...")
            
            inputs = self.driver.find_elements(By.CSS_SELECTOR, "input.sc-fBWQRz.bjSLyk")
            
            if len(inputs) < 2:
                logger.error("Could not find captcha input field")
                return False
            
            # Second input is for captcha
            captcha_input = inputs[1]
            self.driver.execute_script("arguments[0].value = arguments[1];", captcha_input, captcha_text)
            
            # Verify
            current_value = captcha_input.get_attribute("value")
            if current_value == captcha_text:
                logger.info("✅ Captcha filled successfully")
                return True
            else:
                logger.error(f"Captcha fill failed. Expected: {captcha_text}, Got: {current_value}")
                return False
                
        except Exception as e:
            logger.error(f"Step 6 failed: {e}")
            return False
    
    def step7_click_proceed(self):
        """Step 7: Click Proceed button"""
        try:
            logger.info("Step 7: Clicking Proceed button...")
            
            proceed_btn = self.driver.find_element(By.CSS_SELECTOR, "button.button_btn__HeAxz")
            
            if proceed_btn.is_displayed() and proceed_btn.is_enabled():
                self.driver.execute_script("arguments[0].scrollIntoView(true);", proceed_btn)
                time.sleep(1)
                proceed_btn.click()
                logger.info("✅ Proceed button clicked successfully")
                return True
            else:
                logger.error("Proceed button not available")
                return False
                
        except Exception as e:
            logger.error(f"Step 7 failed: {e}")
            return False
    
    def step8_extract_results_from_card(self):
        """Step 8: Extract results from the exact card structure"""
        try:
            logger.info("Step 8: Extracting results from response card...")
            
            # Wait for results page to load
            time.sleep(5)
            
            results = {
                "status": "UNKNOWN",
                "details": [],
                "raw_data": {}
            }
            
            # Check basic status first
            page_source = self.driver.page_source.lower()
            if any(word in page_source for word in ["exists", "valid", "found"]):
                results["status"] = "EXISTS"
                logger.info("✅ Aadhaar validation successful - EXISTS")
            elif any(word in page_source for word in ["not exist", "invalid", "not found"]):
                results["status"] = "NOT_EXISTS"
                logger.info("❌ Aadhaar validation - NOT EXISTS")
            
            # Now extract from the specific card structure
            try:
                # Find the response card
                logger.info("🔍 Looking for response card...")
                response_card = self.driver.find_element(By.CSS_SELECTOR, 
                    ".check-aadhaar-validity-response__card")
                
                if response_card:
                    logger.info("✅ Found response card!")
                    
                    # Find all verify-display-field divs within the card
                    field_divs = response_card.find_elements(By.CSS_SELECTOR, 
                        ".verify-display-field")
                    
                    logger.info(f"Found {len(field_divs)} field divs")
                    
                    for i, field_div in enumerate(field_divs):
                        try:
                            # Get the label and value
                            label_span = field_div.find_element(By.CSS_SELECTOR, 
                                ".verify-display-field__label")
                            
                            # Get all spans and find the value (not the label)
                            all_spans = field_div.find_elements(By.TAG_NAME, "span")
                            
                            label_text = label_span.text.strip()
                            value_text = ""
                            
                            # Find the span that's not the label
                            for span in all_spans:
                                span_text = span.text.strip()
                                if span_text and span_text != label_text:
                                    value_text = span_text
                                    break
                            
                            if label_text and value_text:
                                logger.info(f"📋 Found field: {label_text} = {value_text}")
                                
                                # Store the data
                                results["details"].append(f"{label_text}: {value_text}")
                                results["raw_data"][label_text.lower().replace(" ", "_")] = value_text
                                
                                # Also store with common names
                                label_lower = label_text.lower()
                                if "age" in label_lower:
                                    results["raw_data"]["age_band"] = value_text
                                elif "gender" in label_lower:
                                    results["raw_data"]["gender"] = value_text
                                elif "state" in label_lower:
                                    results["raw_data"]["state"] = value_text
                                elif "mobile" in label_lower:
                                    results["raw_data"]["mobile"] = value_text
                            
                        except Exception as e:
                            logger.warning(f"Error processing field {i+1}: {e}")
                            continue
                    
                    logger.info(f"✅ Successfully extracted {len(results['details'])} fields from card")
                    
                else:
                    logger.warning("Response card not found")
                    
            except Exception as e:
                logger.warning(f"Error finding response card: {e}")
                
                # Fallback: try to find verify-display-field divs anywhere on page
                logger.info("🔍 Trying fallback: searching entire page for field divs...")
                
                try:
                    field_divs = self.driver.find_elements(By.CSS_SELECTOR, 
                        ".verify-display-field")
                    
                    logger.info(f"Found {len(field_divs)} field divs on entire page")
                    
                    for i, field_div in enumerate(field_divs):
                        try:
                            label_span = field_div.find_element(By.CSS_SELECTOR, 
                                ".verify-display-field__label")
                            
                            all_spans = field_div.find_elements(By.TAG_NAME, "span")
                            
                            label_text = label_span.text.strip()
                            value_text = ""
                            
                            for span in all_spans:
                                span_text = span.text.strip()
                                if span_text and span_text != label_text:
                                    value_text = span_text
                                    break
                            
                            if label_text and value_text:
                                logger.info(f"📋 Fallback found: {label_text} = {value_text}")
                                results["details"].append(f"{label_text}: {value_text}")
                                results["raw_data"][label_text.lower().replace(" ", "_")] = value_text
                            
                        except Exception as e:
                            logger.warning(f"Error processing fallback field {i+1}: {e}")
                            continue
                            
                except Exception as e:
                    logger.error(f"Fallback extraction failed: {e}")
            
            # Save page source for debugging
            try:
                with open("final_results_page.html", "w", encoding="utf-8") as f:
                    f.write(self.driver.page_source)
                logger.info("Results page saved to final_results_page.html")
            except:
                pass
            
            logger.info(f"✅ Extraction complete. Found {len(results['details'])} details")
            return results
            
        except Exception as e:
            logger.error(f"Step 8 failed: {e}")
            return {"status": "ERROR", "error": str(e)}
    
    def step9_display_results(self, results):
        """Step 9: Display final results"""
        try:
            logger.info("Step 9: Displaying results...")
            
            root = tk.Tk()
            root.title("🎯 Aadhaar Validation Results")
            root.geometry("600x500")
            root.eval('tk::PlaceWindow . center')
            
            # Title
            tk.Label(root, text="🎯 Aadhaar Validation Complete!", 
                    font=("Arial", 18, "bold"), fg="#2E86AB").pack(pady=20)
            
            # Status
            status = results.get("status", "UNKNOWN")
            if status == "EXISTS":
                status_text = "✅ Aadhaar Number EXISTS"
                status_color = "#28A745"
            elif status == "NOT_EXISTS":
                status_text = "❌ Aadhaar Number NOT EXISTS"
                status_color = "#DC3545"
            else:
                status_text = f"❓ Status: {status}"
                status_color = "#FFC107"
            
            tk.Label(root, text=status_text, 
                    font=("Arial", 16, "bold"), fg=status_color).pack(pady=10)
            
            # Details
            if results.get("details"):
                details_frame = tk.LabelFrame(root, text="📋 Detailed Information", 
                                            font=("Arial", 14, "bold"), padx=20, pady=15)
                details_frame.pack(pady=20, padx=20, fill="both", expand=True)
                
                for detail in results["details"]:
                    detail_frame = tk.Frame(details_frame)
                    detail_frame.pack(fill="x", pady=5)
                    
                    # Split label and value for better formatting
                    if ":" in detail:
                        label, value = detail.split(":", 1)
                        tk.Label(detail_frame, text=f"• {label.strip()}:", 
                                font=("Arial", 12, "bold"), anchor="w").pack(side="left")
                        tk.Label(detail_frame, text=value.strip(), 
                                font=("Arial", 12), anchor="w", fg="#333").pack(side="left", padx=(10, 0))
                    else:
                        tk.Label(detail_frame, text=f"• {detail}", 
                                font=("Arial", 12), anchor="w").pack(fill="x")
            else:
                tk.Label(root, text="⚠️ No additional details found", 
                        font=("Arial", 12), fg="#FFC107").pack(pady=10)
            
            # Close button
            tk.Button(root, text="Close", command=root.destroy,
                     font=("Arial", 12, "bold"), bg="#6C757D", fg="white",
                     padx=30, pady=10).pack(pady=20)
            
            root.mainloop()
            
        except Exception as e:
            logger.error(f"Step 9 failed: {e}")
    
    def run_final(self):
        """Run final working validation"""
        try:
            logger.info("🚀 Starting FINAL WORKING Aadhaar Validator")
            
            if not self.setup_driver():
                messagebox.showerror("Error", "Failed to setup browser")
                return
            
            # Execute all steps
            if not self.step1_load_website():
                return
            
            aadhaar = self.step2_get_aadhaar()
            if not aadhaar:
                return
            
            if not self.step3_fill_aadhaar(aadhaar):
                return
            
            captcha_image = self.step4_extract_captcha()
            if not captcha_image:
                messagebox.showerror("Error", "Could not extract captcha image")
                return
            
            captcha_text = self.step5_show_captcha_gui(captcha_image)
            if not captcha_text:
                return
            
            if not self.step6_fill_captcha(captcha_text):
                return
            
            if not self.step7_click_proceed():
                return
            
            # Extract results using the exact card structure
            results = self.step8_extract_results_from_card()
            
            # Display results
            self.step9_display_results(results)
            
            logger.info("🎉 FINAL validation completed successfully!")
            
        except Exception as e:
            logger.error(f"Final validation failed: {e}")
            messagebox.showerror("Error", f"An error occurred: {e}")
        finally:
            if self.driver:
                self.driver.quit()
                logger.info("Browser closed")

if __name__ == "__main__":
    validator = FinalWorkingAadhaarValidator()
    validator.run_final()
