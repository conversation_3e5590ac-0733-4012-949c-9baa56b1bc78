#!/usr/bin/env python3
"""
Manual Results Check - Complete the process manually and inspect results
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import time

def manual_results_check():
    """Open website and let user complete manually, then inspect results"""
    
    print("🔍 Manual Results Check")
    print("=" * 40)
    
    # Setup
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        # Load website
        url = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
        print(f"Opening: {url}")
        driver.get(url)
        time.sleep(5)
        
        print(f"✅ Website loaded: {driver.title}")
        
        print("\n📋 MANUAL INSTRUCTIONS:")
        print("1. The browser window is now open")
        print("2. Please complete the following steps MANUALLY:")
        print("   a. Enter your Aadhaar number")
        print("   b. Solve the captcha")
        print("   c. Click the Proceed button")
        print("   d. Wait for the results page to load")
        print("3. Once you see the results, come back here")
        
        input("\nPress Enter AFTER you have completed the validation and can see the results...")
        
        print("\n🔍 INSPECTING RESULTS PAGE...")
        
        # Get current page info
        current_url = driver.current_url
        page_title = driver.title
        
        print(f"Current URL: {current_url}")
        print(f"Page Title: {page_title}")
        
        # Get all visible text
        try:
            body = driver.find_element("tag name", "body")
            visible_text = body.text
            
            # Save visible text
            with open("manual_results_text.txt", "w", encoding="utf-8") as f:
                f.write(f"URL: {current_url}\n")
                f.write(f"Title: {page_title}\n")
                f.write(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("="*50 + "\n")
                f.write(visible_text)
            
            print("✅ Visible text saved to manual_results_text.txt")
            
            # Show preview
            print(f"\nVisible text preview (first 1000 chars):")
            print("-" * 50)
            print(visible_text[:1000])
            print("-" * 50)
            
            # Look for key information
            text_lower = visible_text.lower()
            
            print(f"\n🔍 SEARCHING FOR KEY INFORMATION:")
            
            keywords = ['age', 'gender', 'state', 'mobile', 'male', 'female', 'years', 'exists', 'valid']
            found_keywords = []
            
            for keyword in keywords:
                if keyword in text_lower:
                    found_keywords.append(keyword)
            
            print(f"Found keywords: {found_keywords}")
            
            # Look for specific patterns
            import re
            
            patterns = {
                "Numbers (potential age)": r'\d+-\d+',
                "Years": r'\d+\s*years?',
                "Gender words": r'\b(male|female)\b',
                "State-like words": r'\b[A-Z][a-z]{4,15}\b',
                "Masked mobile": r'\*+\d+'
            }
            
            for pattern_name, pattern in patterns.items():
                matches = re.findall(pattern, visible_text, re.IGNORECASE)
                if matches:
                    print(f"{pattern_name}: {matches}")
            
        except Exception as e:
            print(f"Error getting visible text: {e}")
        
        # Save page source
        try:
            page_source = driver.page_source
            with open("manual_results_source.html", "w", encoding="utf-8") as f:
                f.write(page_source)
            print("✅ Page source saved to manual_results_source.html")
        except Exception as e:
            print(f"Error saving page source: {e}")
        
        print(f"\n📁 FILES CREATED:")
        print(f"  - manual_results_text.txt (visible text)")
        print(f"  - manual_results_source.html (full HTML)")
        
        print(f"\n❓ QUESTIONS FOR YOU:")
        print(f"1. Can you see detailed information like Age Band, Gender, State, Mobile?")
        print(f"2. What exactly do you see on the results page?")
        print(f"3. Is the information displayed in a table, list, or other format?")
        
        input("\nPress Enter to close the browser...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        driver.quit()
        print("Browser closed")

if __name__ == "__main__":
    manual_results_check()
