#!/usr/bin/env python3
"""
Manual inspection script - opens website and keeps it open for you to inspect
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import time

def manual_inspection():
    """Open website and keep it open for manual inspection"""
    
    print("🔍 Manual Website Inspection")
    print("=" * 40)
    
    # Setup WebDriver
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        # Navigate to website
        url = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
        print(f"Opening: {url}")
        driver.get(url)
        time.sleep(5)
        
        print(f"✅ Website loaded: {driver.title}")
        print(f"Current URL: {driver.current_url}")
        
        print("\n📋 Instructions:")
        print("1. The browser window is now open")
        print("2. Manually enter an Aadhaar number in the form")
        print("3. Observe what happens - does a captcha appear?")
        print("4. Check the page structure using browser dev tools (F12)")
        print("5. Look for:")
        print("   - Captcha image elements")
        print("   - Captcha input fields") 
        print("   - Submit/Proceed buttons")
        print("6. Note the actual CSS classes and element structure")
        
        print("\n⏳ Browser will stay open until you press Enter...")
        input("Press Enter to close browser and exit...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        driver.quit()
        print("Browser closed")

if __name__ == "__main__":
    manual_inspection()
