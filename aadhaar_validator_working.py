#!/usr/bin/env python3
"""
Working Aadhaar Validator - Handles real website behavior
"""

import time
import base64
import io
import tkinter as tk
from tkinter import messagebox, simpledialog
from PIL import Image, ImageTk
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WorkingAadhaarValidator:
    def __init__(self):
        self.driver = None
        self.target_url = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
        
    def setup_driver(self):
        """Setup WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            logger.info("WebDriver initialized")
            return True
        except Exception as e:
            logger.error(f"WebDriver setup failed: {e}")
            return False
    
    def load_website_and_get_aadhaar(self):
        """Load website first, then get Aadhaar number"""
        try:
            # Step 1: Load website
            logger.info("Step 1: Loading UIDAI website...")
            self.driver.get(self.target_url)
            time.sleep(5)
            logger.info(f"✅ Website loaded: {self.driver.title}")
            
            # Step 2: Get Aadhaar number (AFTER website loads)
            logger.info("Step 2: Getting Aadhaar number...")
            root = tk.Tk()
            root.withdraw()
            
            aadhaar = simpledialog.askstring(
                "Aadhaar Validation", 
                "✅ Website loaded successfully!\n\nEnter your 12-digit Aadhaar number:",
                parent=root
            )
            root.destroy()
            
            if not aadhaar:
                return None
                
            aadhaar = aadhaar.replace(" ", "").replace("-", "")
            if len(aadhaar) == 12 and aadhaar.isdigit():
                logger.info(f"✅ Got Aadhaar: {aadhaar}")
                return aadhaar
            else:
                messagebox.showerror("Invalid", "Please enter a valid 12-digit Aadhaar number")
                return None
                
        except Exception as e:
            logger.error(f"Error in load_website_and_get_aadhaar: {e}")
            return None
    
    def fill_aadhaar_number(self, aadhaar):
        """Fill Aadhaar number in the form"""
        try:
            logger.info("Step 3: Filling Aadhaar number...")
            
            # Find input field
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            logger.info(f"Found {len(inputs)} input elements")
            
            target_input = None
            for i, inp in enumerate(inputs):
                try:
                    input_type = inp.get_attribute("type") or ""
                    input_class = inp.get_attribute("class") or ""
                    is_displayed = inp.is_displayed()
                    is_enabled = inp.is_enabled()
                    
                    logger.info(f"Input {i+1}: type={input_type}, class={input_class[:50]}, displayed={is_displayed}, enabled={is_enabled}")
                    
                    if is_displayed and is_enabled and input_type == "text":
                        target_input = inp
                        logger.info(f"Selected input {i+1} as target")
                        break
                except Exception as e:
                    logger.warning(f"Error checking input {i+1}: {e}")
            
            if not target_input:
                logger.error("No suitable input field found")
                return False
            
            # Fill using JavaScript
            self.driver.execute_script("arguments[0].value = arguments[1];", target_input, aadhaar)
            
            # Verify
            current_value = target_input.get_attribute("value")
            if current_value == aadhaar:
                logger.info("✅ Aadhaar number filled successfully")
                return True
            else:
                logger.error(f"Fill failed. Expected: {aadhaar}, Got: {current_value}")
                return False
                
        except Exception as e:
            logger.error(f"Error filling Aadhaar: {e}")
            return False
    
    def wait_and_find_captcha(self):
        """Wait for captcha to appear and try to find it"""
        try:
            logger.info("Step 4: Waiting for captcha to appear...")
            
            # Try multiple wait times and check for captcha
            wait_times = [2, 3, 5, 8]  # Progressive waiting
            
            for wait_time in wait_times:
                logger.info(f"Waiting {wait_time} seconds...")
                time.sleep(wait_time)
                
                # Check for images
                images = self.driver.find_elements(By.TAG_NAME, "img")
                logger.info(f"Found {len(images)} images")
                
                for i, img in enumerate(images):
                    try:
                        src = img.get_attribute("src") or ""
                        is_displayed = img.is_displayed()
                        
                        if "data:image" in src and is_displayed:
                            logger.info(f"🎯 Found base64 image {i+1}!")
                            
                            # Try to extract it
                            try:
                                base64_data = src.split(",")[1]
                                image_data = base64.b64decode(base64_data)
                                image = Image.open(io.BytesIO(image_data))
                                logger.info("✅ Successfully extracted captcha image")
                                return image
                            except Exception as e:
                                logger.warning(f"Failed to process image: {e}")
                                continue
                                
                    except Exception as e:
                        logger.warning(f"Error checking image {i+1}: {e}")
                        continue
                
                # If we found images but none were captcha, continue waiting
                if images:
                    logger.info(f"Found {len(images)} images but none were captcha, continuing...")
                else:
                    logger.info("No images found yet, continuing...")
            
            logger.warning("No captcha found after all wait attempts")
            return None
            
        except Exception as e:
            logger.error(f"Error in wait_and_find_captcha: {e}")
            return None
    
    def show_captcha_gui(self, captcha_image):
        """Show captcha in GUI and get user input"""
        try:
            logger.info("Step 5: Showing captcha GUI...")
            
            root = tk.Tk()
            root.title("🔐 Solve Captcha")
            root.geometry("500x400")
            root.eval('tk::PlaceWindow . center')
            
            # Title
            tk.Label(root, text="🔐 Captcha Verification", 
                    font=("Arial", 16, "bold"), fg="#2E86AB").pack(pady=10)
            
            # Display image
            display_image = captcha_image.resize((300, 150), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(display_image)
            
            img_frame = tk.Frame(root, relief="solid", bd=2)
            img_frame.pack(pady=10)
            img_label = tk.Label(img_frame, image=photo, bg="white")
            img_label.pack(padx=10, pady=10)
            
            # Input
            tk.Label(root, text="Enter the captcha text:", 
                    font=("Arial", 12)).pack(pady=5)
            
            captcha_entry = tk.Entry(root, font=("Arial", 16), width=15, justify="center")
            captcha_entry.pack(pady=5)
            captcha_entry.focus()
            
            captcha_text = None
            
            def submit():
                nonlocal captcha_text
                text = captcha_entry.get().strip()
                if text:
                    captcha_text = text
                    root.destroy()
                else:
                    messagebox.showerror("Error", "Please enter the captcha text")
            
            def on_enter(event):
                submit()
            
            captcha_entry.bind('<Return>', on_enter)
            
            # Buttons
            button_frame = tk.Frame(root)
            button_frame.pack(pady=20)
            
            tk.Button(button_frame, text="✓ Submit", command=submit,
                     font=("Arial", 12, "bold"), bg="#28A745", fg="white",
                     padx=20, pady=5).pack(side=tk.LEFT, padx=10)
            
            tk.Button(button_frame, text="✗ Cancel", command=root.destroy,
                     font=("Arial", 12), bg="#DC3545", fg="white",
                     padx=20, pady=5).pack(side=tk.LEFT, padx=10)
            
            root.mainloop()
            
            if captcha_text:
                logger.info(f"✅ Got captcha solution: {captcha_text}")
            
            return captcha_text
            
        except Exception as e:
            logger.error(f"Error in captcha GUI: {e}")
            return None
    
    def complete_manually(self, aadhaar, captcha_text=None):
        """Show manual completion instructions"""
        try:
            message = f"✅ PROGRESS SO FAR:\n\n"
            message += f"✓ Website loaded successfully\n"
            message += f"✓ Aadhaar number filled: {aadhaar}\n"
            
            if captcha_text:
                message += f"✓ Captcha solved: {captcha_text}\n\n"
                message += f"NEXT STEPS:\n"
                message += f"1. Fill the captcha '{captcha_text}' in the website\n"
                message += f"2. Click the Proceed button\n"
                message += f"3. View your validation results\n\n"
            else:
                message += f"⚠ Captcha not found automatically\n\n"
                message += f"NEXT STEPS:\n"
                message += f"1. Look for the captcha in the browser window\n"
                message += f"2. Solve the captcha manually\n"
                message += f"3. Click the Proceed button\n"
                message += f"4. View your validation results\n\n"
            
            message += f"The browser window will remain open for you to complete the process."
            
            messagebox.showinfo("Manual Completion Required", message)
            
        except Exception as e:
            logger.error(f"Error in complete_manually: {e}")
    
    def run(self):
        """Main execution"""
        try:
            logger.info("🚀 Starting Working Aadhaar Validator")
            
            # Setup
            if not self.setup_driver():
                messagebox.showerror("Error", "Failed to setup browser")
                return
            
            # Load website and get Aadhaar (correct order!)
            aadhaar = self.load_website_and_get_aadhaar()
            if not aadhaar:
                logger.info("User cancelled or invalid Aadhaar")
                return
            
            # Fill Aadhaar
            if not self.fill_aadhaar_number(aadhaar):
                messagebox.showerror("Error", "Failed to fill Aadhaar number")
                return
            
            # Wait and find captcha
            captcha_image = self.wait_and_find_captcha()
            
            captcha_text = None
            if captcha_image:
                # Show captcha GUI
                captcha_text = self.show_captcha_gui(captcha_image)
            
            # Complete manually (with or without captcha)
            self.complete_manually(aadhaar, captcha_text)
            
            # Keep browser open
            input("Press Enter to close browser and exit...")
            
        except Exception as e:
            logger.error(f"Error in main execution: {e}")
            messagebox.showerror("Error", f"An error occurred: {e}")
        finally:
            if self.driver:
                self.driver.quit()
                logger.info("Browser closed")

if __name__ == "__main__":
    validator = WorkingAadhaarValidator()
    validator.run()
