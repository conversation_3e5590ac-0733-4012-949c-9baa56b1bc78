#!/usr/bin/env python3
"""
Debug Results Page - See exactly what's on the results page
"""

import time
import tkinter as tk
from tkinter import messagebox, simpledialog
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import re

def debug_results_page():
    """Debug the results page to see what data is available"""
    
    print("🔍 Debug Results Page")
    
    # Setup
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        # Load website
        print("Loading website...")
        driver.get("https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en")
        time.sleep(5)
        
        # Get Aadhaar
        root = tk.Tk()
        root.withdraw()
        aadhaar = simpledialog.askstring("Debug", "Enter Aadhaar number:", parent=root)
        root.destroy()
        
        if not aadhaar:
            return
        
        # Fill Aadhaar
        print("Filling Aadhaar...")
        inputs = driver.find_elements(By.CSS_SELECTOR, "input.sc-fBWQRz.bjSLyk")
        if inputs:
            driver.execute_script("arguments[0].value = arguments[1];", inputs[0], aadhaar)
            print(f"✅ Aadhaar filled: {inputs[0].get_attribute('value')}")
        
        # Extract captcha
        print("Looking for captcha...")
        time.sleep(2)
        captcha_img = driver.find_element(By.CSS_SELECTOR, "img.auth-form__captcha-image.check-aadhaar-validity__captcha-image")
        
        if captcha_img:
            print("✅ Captcha found")
            
            # Get captcha from user
            root = tk.Tk()
            root.withdraw()
            captcha_text = simpledialog.askstring("Debug", "Enter captcha text:", parent=root)
            root.destroy()
            
            if captcha_text and len(inputs) > 1:
                # Fill captcha
                driver.execute_script("arguments[0].value = arguments[1];", inputs[1], captcha_text)
                print(f"✅ Captcha filled: {captcha_text}")
                
                # Click proceed
                proceed_btn = driver.find_element(By.CSS_SELECTOR, "button.button_btn__HeAxz")
                if proceed_btn:
                    proceed_btn.click()
                    print("✅ Proceed button clicked")
                    
                    # Wait for results
                    print("Waiting for results...")
                    time.sleep(5)
                    
                    # DEBUG THE RESULTS PAGE
                    print("\n" + "="*50)
                    print("DEBUGGING RESULTS PAGE")
                    print("="*50)
                    
                    # Get page source
                    page_source = driver.page_source
                    
                    # Save full page source
                    with open("debug_results_full.html", "w", encoding="utf-8") as f:
                        f.write(page_source)
                    print("✅ Full page source saved to debug_results_full.html")
                    
                    # Get visible text
                    body = driver.find_element(By.TAG_NAME, "body")
                    visible_text = body.text
                    
                    # Save visible text
                    with open("debug_results_text.txt", "w", encoding="utf-8") as f:
                        f.write(visible_text)
                    print("✅ Visible text saved to debug_results_text.txt")
                    
                    print(f"\nVisible text preview (first 500 chars):")
                    print("-" * 40)
                    print(visible_text[:500])
                    print("-" * 40)
                    
                    # Look for specific patterns
                    print("\n🔍 SEARCHING FOR PATTERNS:")
                    
                    patterns = {
                        "Age Band": [r'age\s*band[:\s]*([^\n\r]+)', r'age[:\s]*(\d+-\d+\s*years?)', r'(\d+-\d+\s*years?)'],
                        "Gender": [r'gender[:\s]*([^\n\r]+)', r'(male|female)'],
                        "State": [r'state[:\s]*([^\n\r]+)', r'location[:\s]*([^\n\r]+)'],
                        "Mobile": [r'mobile[:\s]*([^\n\r]+)', r'phone[:\s]*([^\n\r]+)', r'(\*+\d+)']
                    }
                    
                    for field, field_patterns in patterns.items():
                        print(f"\n{field}:")
                        found = False
                        for pattern in field_patterns:
                            matches = re.findall(pattern, visible_text, re.IGNORECASE)
                            if matches:
                                print(f"  Pattern '{pattern}' found: {matches}")
                                found = True
                        if not found:
                            print(f"  No matches found")
                    
                    # Check all elements with text
                    print(f"\n🔍 CHECKING ALL TEXT ELEMENTS:")
                    
                    all_elements = driver.find_elements(By.XPATH, "//*[text()]")
                    relevant_elements = []
                    
                    for element in all_elements:
                        try:
                            text = element.text.strip()
                            if text and any(keyword in text.lower() for keyword in ['age', 'gender', 'state', 'mobile', 'male', 'female', 'years']):
                                relevant_elements.append(text)
                        except:
                            continue
                    
                    print(f"Found {len(relevant_elements)} relevant text elements:")
                    for i, text in enumerate(relevant_elements[:10]):  # Show first 10
                        print(f"  {i+1}: {text}")
                    
                    # Check table structure
                    print(f"\n🔍 CHECKING TABLE STRUCTURE:")
                    tables = driver.find_elements(By.TAG_NAME, "table")
                    print(f"Found {len(tables)} tables")
                    
                    for i, table in enumerate(tables):
                        try:
                            table_text = table.text.strip()
                            if table_text:
                                print(f"Table {i+1} text: {table_text[:200]}...")
                        except:
                            continue
                    
                    # Check div structure
                    print(f"\n🔍 CHECKING DIV STRUCTURE:")
                    divs = driver.find_elements(By.TAG_NAME, "div")
                    relevant_divs = []
                    
                    for div in divs:
                        try:
                            text = div.text.strip()
                            if text and len(text) < 200 and any(keyword in text.lower() for keyword in ['age', 'gender', 'state', 'mobile']):
                                relevant_divs.append(text)
                        except:
                            continue
                    
                    print(f"Found {len(relevant_divs)} relevant divs:")
                    for i, text in enumerate(relevant_divs[:5]):  # Show first 5
                        print(f"  {i+1}: {text}")
                    
                    print(f"\n✅ Debug complete! Check the saved files:")
                    print(f"  - debug_results_full.html (full page source)")
                    print(f"  - debug_results_text.txt (visible text)")
                    
                    # Ask user what they see
                    root = tk.Tk()
                    root.withdraw()
                    user_input = messagebox.askquestion("Manual Check", 
                        "Please look at the browser window.\n\n"
                        "Can you see the detailed information like:\n"
                        "- Age Band\n"
                        "- Gender\n" 
                        "- State\n"
                        "- Mobile\n\n"
                        "Are these details visible on the page?")
                    root.destroy()
                    
                    if user_input == 'yes':
                        print("✅ User confirms details are visible")
                        
                        # Ask user to describe what they see
                        root = tk.Tk()
                        root.withdraw()
                        user_description = simpledialog.askstring("Describe", 
                            "Please describe exactly what you see:\n"
                            "(e.g., 'Age Band: 20-30 years, Gender: MALE')", parent=root)
                        root.destroy()
                        
                        if user_description:
                            print(f"User description: {user_description}")
                            
                            # Save user description
                            with open("user_description.txt", "w", encoding="utf-8") as f:
                                f.write(user_description)
                            print("✅ User description saved to user_description.txt")
                    else:
                        print("❌ User says details are not visible")
                    
                    input("Press Enter to close...")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_results_page()
