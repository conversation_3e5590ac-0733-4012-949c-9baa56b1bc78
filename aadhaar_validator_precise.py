#!/usr/bin/env python3
"""
PRECISE Aadhaar Validator - Uses exact name attributes
Targets: input[name="uid"] and input[name="captcha"]
"""

import time
import base64
import io
import tkinter as tk
from tkinter import messagebox, simpledialog
from PIL import Image, ImageTk
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PreciseAadhaarValidator:
    def __init__(self):
        self.driver = None
        self.target_url = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
        
    def setup_driver(self):
        """Setup WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            logger.info("WebDriver initialized")
            return True
        except Exception as e:
            logger.error(f"WebDriver setup failed: {e}")
            return False
    
    def load_website_and_get_aadhaar(self):
        """Load website first, then get Aadhaar number"""
        try:
            # Step 1: Load website FIRST
            logger.info("Step 1: Loading UIDAI website...")
            self.driver.get(self.target_url)
            time.sleep(5)
            logger.info(f"✅ Website loaded: {self.driver.title}")
            
            # Step 2: Get Aadhaar number AFTER website loads
            root = tk.Tk()
            root.withdraw()
            
            aadhaar = simpledialog.askstring(
                "Aadhaar Validation", 
                "✅ Website loaded successfully!\n\nEnter your 12-digit Aadhaar number:",
                parent=root
            )
            root.destroy()
            
            if not aadhaar:
                return None
                
            aadhaar = aadhaar.replace(" ", "").replace("-", "")
            if len(aadhaar) == 12 and aadhaar.isdigit():
                logger.info(f"✅ Got Aadhaar: {aadhaar}")
                return aadhaar
            else:
                messagebox.showerror("Invalid", "Please enter a valid 12-digit Aadhaar number")
                return None
                
        except Exception as e:
            logger.error(f"Error in load_website_and_get_aadhaar: {e}")
            return None
    
    def fill_aadhaar_precisely(self, aadhaar):
        """Fill Aadhaar using precise name selector"""
        try:
            logger.info("Step 3: Filling Aadhaar number using name='uid'...")
            
            # Find Aadhaar input using name attribute
            aadhaar_input = self.driver.find_element(By.CSS_SELECTOR, "input[name='uid']")
            logger.info("✅ Found Aadhaar input using name='uid'")
            
            # Scroll to element
            self.driver.execute_script("arguments[0].scrollIntoView(true);", aadhaar_input)
            time.sleep(1)
            
            # Try multiple filling methods
            filling_methods = [
                ("React Events Method", self._fill_with_react_events),
                ("Human Typing Method", self._fill_like_human),
                ("Focus and Type Method", self._fill_with_focus),
                ("Click and Type Method", self._fill_with_click)
            ]
            
            for method_name, method_func in filling_methods:
                try:
                    logger.info(f"Trying {method_name}...")
                    
                    # Clear field first
                    self.driver.execute_script("arguments[0].value = '';", aadhaar_input)
                    time.sleep(0.5)
                    
                    # Apply filling method
                    success = method_func(aadhaar_input, aadhaar)
                    
                    if success:
                        # Wait and verify
                        time.sleep(1)
                        current_value = aadhaar_input.get_attribute("value")
                        js_value = self.driver.execute_script("return arguments[0].value;", aadhaar_input)
                        
                        logger.info(f"Attribute value: {current_value}")
                        logger.info(f"JavaScript value: {js_value}")
                        
                        if current_value == aadhaar or js_value == aadhaar:
                            logger.info(f"✅ Aadhaar filled successfully using {method_name}")
                            
                            # Ask user for visual confirmation
                            root = tk.Tk()
                            root.withdraw()
                            user_confirms = messagebox.askyesno("Visual Confirmation", 
                                f"Method: {method_name}\n\n"
                                f"Script shows: {current_value}\n\n"
                                f"Do you see '{aadhaar}' filled in the Aadhaar field in the browser?")
                            root.destroy()
                            
                            if user_confirms:
                                logger.info(f"✅ User confirms {method_name} works visually")
                                return True
                            else:
                                logger.warning(f"User says {method_name} didn't work visually")
                                continue
                        else:
                            logger.warning(f"{method_name} failed verification")
                            continue
                    
                except Exception as e:
                    logger.warning(f"{method_name} failed: {e}")
                    continue
            
            logger.error("All Aadhaar filling methods failed")
            return False
            
        except Exception as e:
            logger.error(f"Error filling Aadhaar: {e}")
            return False
    
    def _fill_with_react_events(self, element, text):
        """Fill with comprehensive React/Angular event triggering"""
        try:
            script = """
            var element = arguments[0];
            var text = arguments[1];
            
            // Focus the element
            element.focus();
            
            // Clear existing value
            element.value = '';
            
            // Set the value
            element.value = text;
            
            // Trigger all possible events
            var events = [
                'focus', 'input', 'change', 'keydown', 'keyup', 'keypress', 
                'blur', 'textInput', 'compositionstart', 'compositionend'
            ];
            
            events.forEach(function(eventType) {
                var event;
                if (eventType === 'input' || eventType === 'change') {
                    event = new Event(eventType, { bubbles: true, cancelable: true });
                } else if (eventType.startsWith('key')) {
                    event = new KeyboardEvent(eventType, { bubbles: true, cancelable: true });
                } else {
                    event = new Event(eventType, { bubbles: true, cancelable: true });
                }
                element.dispatchEvent(event);
            });
            
            // React synthetic events
            var reactEvent = new Event('input', { bubbles: true });
            reactEvent.simulated = true;
            element.dispatchEvent(reactEvent);
            
            // Force React to update
            if (element._valueTracker) {
                element._valueTracker.setValue('');
            }
            
            return element.value;
            """
            
            result = self.driver.execute_script(script, element, text)
            logger.info(f"React events result: {result}")
            return True
            
        except Exception as e:
            logger.error(f"React events method failed: {e}")
            return False
    
    def _fill_like_human(self, element, text):
        """Fill by simulating human typing"""
        try:
            # Click to focus
            element.click()
            time.sleep(0.5)
            
            # Clear field
            element.send_keys(Keys.CONTROL + "a")
            time.sleep(0.2)
            element.send_keys(Keys.DELETE)
            time.sleep(0.5)
            
            # Type character by character with human-like delays
            for i, char in enumerate(text):
                element.send_keys(char)
                # Variable delay between 50-150ms
                delay = 0.05 + (i % 3) * 0.03
                time.sleep(delay)
            
            # Final blur to trigger validation
            self.driver.execute_script("arguments[0].blur();", element)
            time.sleep(0.2)
            
            return True
            
        except Exception as e:
            logger.error(f"Human typing method failed: {e}")
            return False
    
    def _fill_with_focus(self, element, text):
        """Fill with focus and direct typing"""
        try:
            # Focus using JavaScript
            self.driver.execute_script("arguments[0].focus();", element)
            time.sleep(0.5)
            
            # Clear and type
            element.send_keys(Keys.CONTROL + "a")
            time.sleep(0.2)
            element.send_keys(text)
            time.sleep(0.5)
            
            # Trigger change event
            self.driver.execute_script("arguments[0].dispatchEvent(new Event('change', {bubbles: true}));", element)
            
            return True
            
        except Exception as e:
            logger.error(f"Focus method failed: {e}")
            return False
    
    def _fill_with_click(self, element, text):
        """Fill with click and type"""
        try:
            element.click()
            time.sleep(0.5)
            element.clear()
            time.sleep(0.5)
            element.send_keys(text)
            time.sleep(0.5)
            
            return True
            
        except Exception as e:
            logger.error(f"Click method failed: {e}")
            return False
    
    def extract_captcha_image(self):
        """Extract captcha image"""
        try:
            logger.info("Step 4: Extracting captcha image...")
            time.sleep(2)
            
            captcha_img = self.driver.find_element(By.CSS_SELECTOR, 
                "img.auth-form__captcha-image.check-aadhaar-validity__captcha-image")
            
            if captcha_img.is_displayed():
                src = captcha_img.get_attribute("src")
                
                if "data:" in src:
                    base64_data = src.split(",")[1]
                    image_data = base64.b64decode(base64_data)
                    image = Image.open(io.BytesIO(image_data))
                    logger.info("✅ Captcha image extracted")
                    return image
                        
            logger.error("Could not extract captcha image")
            return None
            
        except Exception as e:
            logger.error(f"Captcha extraction failed: {e}")
            return None
    
    def show_captcha_gui(self, captcha_image):
        """Show captcha GUI"""
        try:
            root = tk.Tk()
            root.title("🔐 Solve Captcha")
            root.geometry("500x400")
            root.eval('tk::PlaceWindow . center')
            
            tk.Label(root, text="🔐 Solve Captcha", 
                    font=("Arial", 16, "bold"), fg="#2E86AB").pack(pady=10)
            
            display_image = captcha_image.resize((300, 150), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(display_image)
            
            img_frame = tk.Frame(root, relief="solid", bd=2)
            img_frame.pack(pady=10)
            img_label = tk.Label(img_frame, image=photo, bg="white")
            img_label.pack(padx=10, pady=10)
            
            tk.Label(root, text="Enter the captcha text:", font=("Arial", 12)).pack(pady=5)
            
            captcha_entry = tk.Entry(root, font=("Arial", 16), width=15, justify="center")
            captcha_entry.pack(pady=5)
            captcha_entry.focus()
            
            captcha_text = None
            
            def submit():
                nonlocal captcha_text
                text = captcha_entry.get().strip()
                if text:
                    captcha_text = text
                    root.destroy()
                else:
                    messagebox.showerror("Error", "Please enter the captcha text")
            
            captcha_entry.bind('<Return>', lambda e: submit())
            
            button_frame = tk.Frame(root)
            button_frame.pack(pady=20)
            
            tk.Button(button_frame, text="✓ Submit", command=submit,
                     font=("Arial", 12, "bold"), bg="#28A745", fg="white",
                     padx=20, pady=5).pack(side=tk.LEFT, padx=10)
            
            tk.Button(button_frame, text="✗ Cancel", command=root.destroy,
                     font=("Arial", 12), bg="#DC3545", fg="white",
                     padx=20, pady=5).pack(side=tk.LEFT, padx=10)
            
            root.mainloop()
            return captcha_text
            
        except Exception as e:
            logger.error(f"Captcha GUI failed: {e}")
            return None
    
    def fill_captcha_precisely(self, captcha_text):
        """Fill captcha using precise name selector"""
        try:
            logger.info("Step 6: Filling captcha using name='captcha'...")
            
            # Find captcha input using name attribute
            captcha_input = self.driver.find_element(By.CSS_SELECTOR, "input[name='captcha']")
            logger.info("✅ Found captcha input using name='captcha'")
            
            # Use the same filling methods as Aadhaar
            filling_methods = [
                ("React Events Method", self._fill_with_react_events),
                ("Human Typing Method", self._fill_like_human),
                ("Focus and Type Method", self._fill_with_focus),
                ("Click and Type Method", self._fill_with_click)
            ]
            
            for method_name, method_func in filling_methods:
                try:
                    logger.info(f"Trying captcha {method_name}...")
                    
                    # Clear field first
                    self.driver.execute_script("arguments[0].value = '';", captcha_input)
                    time.sleep(0.5)
                    
                    # Apply filling method
                    success = method_func(captcha_input, captcha_text)
                    
                    if success:
                        # Wait and verify
                        time.sleep(1)
                        current_value = captcha_input.get_attribute("value")
                        js_value = self.driver.execute_script("return arguments[0].value;", captcha_input)
                        
                        logger.info(f"Captcha attribute value: {current_value}")
                        logger.info(f"Captcha JavaScript value: {js_value}")
                        
                        if current_value == captcha_text or js_value == captcha_text:
                            logger.info(f"✅ Captcha filled successfully using {method_name}")
                            
                            # Ask user for visual confirmation
                            root = tk.Tk()
                            root.withdraw()
                            user_confirms = messagebox.askyesno("Captcha Visual Confirmation", 
                                f"Method: {method_name}\n\n"
                                f"Script shows: {current_value}\n\n"
                                f"Do you see '{captcha_text}' filled in the captcha field in the browser?")
                            root.destroy()
                            
                            if user_confirms:
                                logger.info(f"✅ User confirms captcha {method_name} works visually")
                                return True
                            else:
                                logger.warning(f"User says captcha {method_name} didn't work visually")
                                continue
                        else:
                            logger.warning(f"Captcha {method_name} failed verification")
                            continue
                    
                except Exception as e:
                    logger.warning(f"Captcha {method_name} failed: {e}")
                    continue
            
            logger.error("All captcha filling methods failed")
            return False
            
        except Exception as e:
            logger.error(f"Error filling captcha: {e}")
            return False
    
    def click_proceed_and_extract_results(self):
        """Click proceed and extract results"""
        try:
            logger.info("Step 7: Clicking Proceed button...")
            
            proceed_btn = self.driver.find_element(By.CSS_SELECTOR, "button.button_btn__HeAxz")
            proceed_btn.click()
            logger.info("✅ Proceed button clicked")
            
            # Wait for results
            time.sleep(5)
            
            # Extract results using exact structure
            logger.info("Step 8: Extracting results...")
            
            results = {"status": "UNKNOWN", "details": []}
            
            try:
                response_card = self.driver.find_element(By.CSS_SELECTOR, 
                    ".check-aadhaar-validity-response__card")
                
                field_divs = response_card.find_elements(By.CSS_SELECTOR, 
                    ".verify-display-field")
                
                logger.info(f"Found {len(field_divs)} result fields")
                
                for field_div in field_divs:
                    try:
                        label_span = field_div.find_element(By.CSS_SELECTOR, 
                            ".verify-display-field__label")
                        
                        all_spans = field_div.find_elements(By.TAG_NAME, "span")
                        
                        label_text = label_span.text.strip()
                        value_text = ""
                        
                        for span in all_spans:
                            span_text = span.text.strip()
                            if span_text and span_text != label_text:
                                value_text = span_text
                                break
                        
                        if label_text and value_text:
                            results["details"].append(f"{label_text}: {value_text}")
                            logger.info(f"📋 {label_text}: {value_text}")
                        
                    except Exception as e:
                        logger.warning(f"Error processing field: {e}")
                        continue
                
                if results["details"]:
                    results["status"] = "EXISTS"
                    logger.info("✅ Results extracted successfully")
                else:
                    results["status"] = "NO_DETAILS"
                    
            except Exception as e:
                logger.error(f"Error extracting results: {e}")
                results["status"] = "ERROR"
            
            return results
            
        except Exception as e:
            logger.error(f"Error in proceed and extract: {e}")
            return {"status": "ERROR", "error": str(e)}
    
    def display_final_results(self, results):
        """Display final results"""
        try:
            root = tk.Tk()
            root.title("🎯 Aadhaar Validation Results")
            root.geometry("600x500")
            root.eval('tk::PlaceWindow . center')
            
            tk.Label(root, text="🎯 Aadhaar Validation Complete!", 
                    font=("Arial", 18, "bold"), fg="#2E86AB").pack(pady=20)
            
            status = results.get("status", "UNKNOWN")
            if status == "EXISTS":
                status_text = "✅ Aadhaar Number EXISTS"
                status_color = "#28A745"
            else:
                status_text = f"❓ Status: {status}"
                status_color = "#FFC107"
            
            tk.Label(root, text=status_text, 
                    font=("Arial", 16, "bold"), fg=status_color).pack(pady=10)
            
            if results.get("details"):
                details_frame = tk.LabelFrame(root, text="📋 Details", 
                                            font=("Arial", 14, "bold"), padx=20, pady=15)
                details_frame.pack(pady=20, padx=20, fill="both", expand=True)
                
                for detail in results["details"]:
                    tk.Label(details_frame, text=f"• {detail}", 
                            font=("Arial", 12), anchor="w").pack(fill="x", pady=2)
            
            tk.Button(root, text="Close", command=root.destroy,
                     font=("Arial", 12, "bold"), bg="#6C757D", fg="white",
                     padx=30, pady=10).pack(pady=20)
            
            root.mainloop()
            
        except Exception as e:
            logger.error(f"Error displaying results: {e}")
    
    def run_precise(self):
        """Run precise validation"""
        try:
            logger.info("🚀 Starting PRECISE Aadhaar Validator")
            
            if not self.setup_driver():
                return
            
            # Load website and get Aadhaar
            aadhaar = self.load_website_and_get_aadhaar()
            if not aadhaar:
                return
            
            # Fill Aadhaar precisely
            if not self.fill_aadhaar_precisely(aadhaar):
                messagebox.showerror("Error", "Could not fill Aadhaar number")
                return
            
            # Extract captcha
            captcha_image = self.extract_captcha_image()
            if not captcha_image:
                messagebox.showerror("Error", "Could not extract captcha")
                return
            
            # Show captcha GUI
            captcha_text = self.show_captcha_gui(captcha_image)
            if not captcha_text:
                return
            
            # Fill captcha precisely
            if not self.fill_captcha_precisely(captcha_text):
                messagebox.showerror("Error", "Could not fill captcha")
                return
            
            # Click proceed and extract results
            results = self.click_proceed_and_extract_results()
            
            # Display results
            self.display_final_results(results)
            
            logger.info("🎉 Precise validation completed!")
            
        except Exception as e:
            logger.error(f"Precise validation failed: {e}")
        finally:
            if self.driver:
                self.driver.quit()

if __name__ == "__main__":
    validator = PreciseAadhaarValidator()
    validator.run_precise()
