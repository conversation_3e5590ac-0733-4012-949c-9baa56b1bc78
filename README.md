# 🎯 High-Performance Aadhaar Validation Automation Script

A robust Python automation script for validating Aadhaar numbers on the official UIDAI website with GUI-based captcha solving.

## 🚀 Features

- **Automated Form Filling**: Automatically fills Aadhaar number and captcha
- **GUI Captcha Solver**: Displays captcha image in a user-friendly GUI
- **Smart Result Detection**: Automatically detects successful form submission
- **Error Handling**: Comprehensive error handling and logging
- **User-Friendly Interface**: Clean GUI for input and results display
- **High Performance**: Optimized WebDriver settings for fast execution

## 📋 Requirements

- Python 3.7+
- Google Chrome browser
- Internet connection

## 🛠️ Installation & Setup

### Option 1: Quick Setup (Recommended)
```bash
python setup_and_run.py
```
This will automatically install all dependencies and run the validator.

### Option 2: Manual Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Run the validator
python aadhaar_validator.py
```

## 📖 Usage

1. **Run the script**:
   ```bash
   python aadhaar_validator.py
   ```

2. **Enter Aadhaar Number**: 
   - A dialog box will appear asking for your 12-digit Aadhaar number
   - Enter the number (spaces and dashes are automatically removed)

3. **Solve Captcha**:
   - The script will extract the captcha image from the website
   - A GUI window will display the captcha image
   - Enter the captcha text and click Submit

4. **View Results**:
   - The script will automatically submit the form
   - Results will be displayed in a GUI window
   - Shows validation status and any additional details

## 🔧 Script Flow

```
1. Initialize WebDriver with optimized settings
2. Navigate to UIDAI Aadhaar validation website
3. Get Aadhaar number from user input
4. Fill Aadhaar number in the form
5. Extract captcha image (base64) from the page
6. Display captcha in GUI for user to solve
7. Fill captcha solution in the form
8. Submit form by clicking "Proceed" button
9. Detect successful submission (form elements disappear)
10. Extract validation results from results page
11. Display results to user
12. Clean up and close browser
```

## 🎯 Target Website Details

- **URL**: https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en
- **Form Elements**:
  - Aadhaar Number: `class="auth-form__text-field"`
  - Captcha Input: `class="auth-form__captcha-field"`
  - Captcha Image: `class="auth-form__captcha-box"`
  - Submit Button: `class="auth-form__button"` in `class="auth-form__button-container"`

## 📊 Expected Results

The script can extract the following information:
- ✅ Aadhaar number existence status
- 📊 Age Band (e.g., "20-30 years")
- 👤 Gender (e.g., "MALE")
- 🏛️ State (e.g., "Maharashtra")
- 📱 Mobile (partially masked, e.g., "*******671")

## 🔍 Error Handling

The script includes comprehensive error handling for:
- WebDriver initialization failures
- Network connectivity issues
- Element not found scenarios
- Captcha extraction failures
- Form submission errors
- Result parsing issues

## 📝 Logging

All operations are logged with timestamps for debugging:
- INFO: Normal operations
- WARNING: Non-critical issues
- ERROR: Critical failures

## ⚡ Performance Optimizations

- Optimized Chrome options for faster loading
- Smart wait strategies using WebDriverWait
- Efficient element location strategies
- Minimal resource usage

## 🛡️ Security Features

- No storage of sensitive information
- Automatic cleanup of resources
- Anti-detection measures for WebDriver

## 🔧 Troubleshooting

### Common Issues:

1. **ChromeDriver not found**:
   - Run `python setup_and_run.py` to auto-install ChromeDriver
   - Or manually install ChromeDriver and add to PATH

2. **Website not loading**:
   - Check internet connection
   - Verify the target URL is accessible
   - Try running with different Chrome options

3. **Captcha not displaying**:
   - Ensure PIL/Pillow is installed correctly
   - Check if the captcha element structure has changed

4. **Form submission failing**:
   - Verify all form fields are filled correctly
   - Check if website structure has changed
   - Review browser console for JavaScript errors

## 📁 File Structure

```
├── aadhaar_validator.py    # Main automation script
├── setup_and_run.py       # Setup and runner script
├── requirements.txt       # Python dependencies
└── README.md             # This file
```

## 🤝 Contributing

Feel free to submit issues, feature requests, or pull requests to improve the script.

## ⚠️ Disclaimer

This script is for educational and legitimate verification purposes only. Users are responsible for complying with the terms of service of the UIDAI website and applicable laws.

## 📄 License

This project is open source and available under the MIT License.
