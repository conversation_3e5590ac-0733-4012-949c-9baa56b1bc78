#!/usr/bin/env python3
"""
Aadhaar Validator with JSON Data Extraction
Extracts data from JavaScript/JSON in script tags
"""

import time
import base64
import io
import json
import re
import tkinter as tk
from tkinter import messagebox, simpledialog
from PIL import Image, ImageTk
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class JsonAadhaarValidator:
    def __init__(self):
        self.driver = None
        self.target_url = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
        
    def setup_driver(self):
        """Setup WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            logger.info("WebDriver initialized")
            return True
        except Exception as e:
            logger.error(f"WebDriver setup failed: {e}")
            return False
    
    def complete_validation_steps(self):
        """Complete all validation steps up to results"""
        try:
            # Step 1: Load website
            logger.info("Loading UIDAI website...")
            self.driver.get(self.target_url)
            time.sleep(5)
            
            # Step 2: Get Aadhaar
            root = tk.Tk()
            root.withdraw()
            aadhaar = simpledialog.askstring("Aadhaar Input", 
                "✅ Website loaded!\nEnter your 12-digit Aadhaar number:", parent=root)
            root.destroy()
            
            if not aadhaar:
                return False
                
            aadhaar = aadhaar.replace(" ", "").replace("-", "")
            if len(aadhaar) != 12 or not aadhaar.isdigit():
                messagebox.showerror("Invalid", "Please enter a valid 12-digit Aadhaar number")
                return False
            
            # Step 3: Fill Aadhaar
            logger.info("Filling Aadhaar number...")
            inputs = self.driver.find_elements(By.CSS_SELECTOR, "input.sc-fBWQRz.bjSLyk")
            if not inputs:
                logger.error("Could not find Aadhaar input field")
                return False
            
            self.driver.execute_script("arguments[0].value = arguments[1];", inputs[0], aadhaar)
            logger.info("✅ Aadhaar filled")
            
            # Step 4: Extract captcha
            logger.info("Extracting captcha...")
            time.sleep(2)
            
            try:
                captcha_img = self.driver.find_element(By.CSS_SELECTOR, 
                    "img.auth-form__captcha-image.check-aadhaar-validity__captcha-image")
                src = captcha_img.get_attribute("src")
                
                if "data:" in src:
                    base64_data = src.split(",")[1]
                    image_data = base64.b64decode(base64_data)
                    image = Image.open(io.BytesIO(image_data))
                    logger.info("✅ Captcha extracted")
                else:
                    logger.error("Captcha not in expected format")
                    return False
            except Exception as e:
                logger.error(f"Captcha extraction failed: {e}")
                return False
            
            # Step 5: Show captcha GUI
            logger.info("Showing captcha GUI...")
            captcha_text = self.show_captcha_gui(image)
            if not captcha_text:
                return False
            
            # Step 6: Fill captcha
            logger.info("Filling captcha...")
            if len(inputs) < 2:
                logger.error("Could not find captcha input field")
                return False
            
            self.driver.execute_script("arguments[0].value = arguments[1];", inputs[1], captcha_text)
            logger.info("✅ Captcha filled")
            
            # Step 7: Click Proceed
            logger.info("Clicking Proceed...")
            try:
                proceed_btn = self.driver.find_element(By.CSS_SELECTOR, "button.button_btn__HeAxz")
                proceed_btn.click()
                logger.info("✅ Proceed clicked")
            except Exception as e:
                logger.error(f"Could not click Proceed: {e}")
                return False
            
            # Wait for results
            time.sleep(5)
            logger.info("✅ Validation steps completed")
            return True
            
        except Exception as e:
            logger.error(f"Validation steps failed: {e}")
            return False
    
    def show_captcha_gui(self, captcha_image):
        """Show captcha GUI"""
        try:
            root = tk.Tk()
            root.title("🔐 Solve Captcha")
            root.geometry("500x400")
            root.eval('tk::PlaceWindow . center')
            
            tk.Label(root, text="🔐 Solve Captcha", 
                    font=("Arial", 16, "bold"), fg="#2E86AB").pack(pady=10)
            
            display_image = captcha_image.resize((300, 150), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(display_image)
            
            img_frame = tk.Frame(root, relief="solid", bd=2)
            img_frame.pack(pady=10)
            img_label = tk.Label(img_frame, image=photo, bg="white")
            img_label.pack(padx=10, pady=10)
            
            tk.Label(root, text="Enter the captcha text:", font=("Arial", 12)).pack(pady=5)
            
            captcha_entry = tk.Entry(root, font=("Arial", 16), width=15, justify="center")
            captcha_entry.pack(pady=5)
            captcha_entry.focus()
            
            captcha_text = None
            
            def submit():
                nonlocal captcha_text
                text = captcha_entry.get().strip()
                if text:
                    captcha_text = text
                    root.destroy()
                else:
                    messagebox.showerror("Error", "Please enter the captcha text")
            
            captcha_entry.bind('<Return>', lambda e: submit())
            
            button_frame = tk.Frame(root)
            button_frame.pack(pady=20)
            
            tk.Button(button_frame, text="✓ Submit", command=submit,
                     font=("Arial", 12, "bold"), bg="#28A745", fg="white",
                     padx=20, pady=5).pack(side=tk.LEFT, padx=10)
            
            tk.Button(button_frame, text="✗ Cancel", command=root.destroy,
                     font=("Arial", 12), bg="#DC3545", fg="white",
                     padx=20, pady=5).pack(side=tk.LEFT, padx=10)
            
            root.mainloop()
            return captcha_text
            
        except Exception as e:
            logger.error(f"Captcha GUI failed: {e}")
            return None
    
    def extract_json_data(self):
        """Extract data from JavaScript/JSON in script tags"""
        try:
            logger.info("🔍 Extracting JSON data from script tags...")
            
            results = {
                "status": "UNKNOWN",
                "details": [],
                "raw_data": {}
            }
            
            # Save page source for debugging
            page_source = self.driver.page_source
            with open("results_page_json.html", "w", encoding="utf-8") as f:
                f.write(page_source)
            logger.info("Page source saved to results_page_json.html")
            
            # Check basic status first
            page_text = page_source.lower()
            if any(word in page_text for word in ["exists", "valid", "found"]):
                results["status"] = "EXISTS"
                logger.info("✅ Aadhaar validation successful - EXISTS")
            elif any(word in page_text for word in ["not exist", "invalid", "not found"]):
                results["status"] = "NOT_EXISTS"
                logger.info("❌ Aadhaar validation - NOT EXISTS")
            
            # Get all script elements
            script_elements = self.driver.find_elements(By.TAG_NAME, "script")
            logger.info(f"Found {len(script_elements)} script elements")
            
            all_script_content = ""
            
            for i, script in enumerate(script_elements):
                try:
                    script_content = script.get_attribute("innerHTML") or ""
                    if script_content:
                        all_script_content += f"\n\n--- SCRIPT {i+1} ---\n{script_content}"
                        
                        # Look for JSON-like data
                        if any(keyword in script_content.lower() for keyword in 
                               ['age', 'gender', 'state', 'mobile', 'male', 'female', 'years']):
                            logger.info(f"📋 Script {i+1} contains potential data")
                            
                            # Try to extract JSON objects
                            json_matches = re.findall(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', script_content)
                            for j, json_str in enumerate(json_matches):
                                try:
                                    json_obj = json.loads(json_str)
                                    logger.info(f"  JSON object {j+1}: {json_obj}")
                                    
                                    # Extract data from JSON
                                    self.extract_from_json_object(json_obj, results)
                                    
                                except json.JSONDecodeError:
                                    continue
                            
                            # Try to extract key-value pairs
                            patterns = {
                                "age": [r'"?age"?\s*:\s*"?([^",\n]+)"?', r'age["\s]*[:=]\s*["\']?([^"\';\n,]+)'],
                                "gender": [r'"?gender"?\s*:\s*"?([^",\n]+)"?', r'gender["\s]*[:=]\s*["\']?([^"\';\n,]+)'],
                                "state": [r'"?state"?\s*:\s*"?([^",\n]+)"?', r'state["\s]*[:=]\s*["\']?([^"\';\n,]+)'],
                                "mobile": [r'"?mobile"?\s*:\s*"?([^",\n]+)"?', r'mobile["\s]*[:=]\s*["\']?([^"\';\n,]+)']
                            }
                            
                            for field, field_patterns in patterns.items():
                                for pattern in field_patterns:
                                    matches = re.findall(pattern, script_content, re.IGNORECASE)
                                    if matches:
                                        for match in matches:
                                            match = match.strip().strip('"\'')
                                            if match and len(match) < 50:  # Reasonable length
                                                logger.info(f"  Found {field}: {match}")
                                                if field not in results["raw_data"]:
                                                    results["raw_data"][field] = match
                                                    results["details"].append(f"{field.title()}: {match}")
                                                break
                                        if matches:
                                            break
                            
                            # Look for specific patterns in script content
                            specific_patterns = {
                                "Age Band": r'(\d+-\d+\s*years?)',
                                "Gender": r'\b(MALE|FEMALE|male|female)\b',
                                "Mobile": r'(\*{3,}\d{3,})',
                                "State": r'\b([A-Z][a-z]{4,15})\b'  # State names
                            }
                            
                            for field, pattern in specific_patterns.items():
                                matches = re.findall(pattern, script_content)
                                if matches:
                                    for match in matches:
                                        match = match.strip()
                                        if field.lower().replace(" ", "_") not in results["raw_data"]:
                                            logger.info(f"  Pattern found {field}: {match}")
                                            results["raw_data"][field.lower().replace(" ", "_")] = match
                                            results["details"].append(f"{field}: {match}")
                                            break
                
                except Exception as e:
                    logger.warning(f"Error processing script {i+1}: {e}")
                    continue
            
            # Save all script content for manual inspection
            with open("all_scripts_content.txt", "w", encoding="utf-8") as f:
                f.write(all_script_content)
            logger.info("All script content saved to all_scripts_content.txt")
            
            # Also try to get data from window object
            try:
                logger.info("🔍 Checking window object...")
                window_data = self.driver.execute_script("return JSON.stringify(window);")
                with open("window_data.json", "w", encoding="utf-8") as f:
                    f.write(window_data)
                logger.info("Window data saved to window_data.json")
                
                # Look for data in window object
                if any(keyword in window_data.lower() for keyword in ['age', 'gender', 'state', 'mobile']):
                    logger.info("Found potential data in window object")
                    # Try to extract from window data
                    self.extract_from_text(window_data, results)
                    
            except Exception as e:
                logger.warning(f"Error getting window data: {e}")
            
            logger.info(f"✅ JSON extraction complete. Found {len(results['details'])} details")
            return results
            
        except Exception as e:
            logger.error(f"JSON extraction failed: {e}")
            return {"status": "ERROR", "error": str(e)}
    
    def extract_from_json_object(self, json_obj, results):
        """Extract data from a JSON object"""
        try:
            if isinstance(json_obj, dict):
                for key, value in json_obj.items():
                    key_lower = str(key).lower()
                    value_str = str(value)
                    
                    if 'age' in key_lower and value_str:
                        results["raw_data"]["age"] = value_str
                        results["details"].append(f"Age: {value_str}")
                    elif 'gender' in key_lower and value_str:
                        results["raw_data"]["gender"] = value_str
                        results["details"].append(f"Gender: {value_str}")
                    elif 'state' in key_lower and value_str:
                        results["raw_data"]["state"] = value_str
                        results["details"].append(f"State: {value_str}")
                    elif 'mobile' in key_lower and value_str:
                        results["raw_data"]["mobile"] = value_str
                        results["details"].append(f"Mobile: {value_str}")
                    
                    # Recursive check for nested objects
                    if isinstance(value, dict):
                        self.extract_from_json_object(value, results)
                        
        except Exception as e:
            logger.warning(f"Error extracting from JSON object: {e}")
    
    def extract_from_text(self, text, results):
        """Extract data from text using patterns"""
        try:
            patterns = {
                "Age Band": r'(\d+-\d+\s*years?)',
                "Gender": r'\b(MALE|FEMALE|male|female)\b',
                "Mobile": r'(\*{3,}\d{3,})',
                "State": r'\b([A-Z][a-z]{4,15})\b'
            }
            
            for field, pattern in patterns.items():
                if field.lower().replace(" ", "_") not in results["raw_data"]:
                    matches = re.findall(pattern, text)
                    if matches:
                        match = matches[0].strip()
                        results["raw_data"][field.lower().replace(" ", "_")] = match
                        results["details"].append(f"{field}: {match}")
                        
        except Exception as e:
            logger.warning(f"Error extracting from text: {e}")
    
    def display_results(self, results):
        """Display results"""
        try:
            root = tk.Tk()
            root.title("🎯 Aadhaar Validation Results")
            root.geometry("500x400")
            root.eval('tk::PlaceWindow . center')
            
            tk.Label(root, text="🎯 Aadhaar Validation Results", 
                    font=("Arial", 18, "bold"), fg="#2E86AB").pack(pady=20)
            
            # Status
            status = results.get("status", "UNKNOWN")
            if status == "EXISTS":
                status_text = "✅ Aadhaar Number EXISTS"
                status_color = "#28A745"
            elif status == "NOT_EXISTS":
                status_text = "❌ Aadhaar Number NOT EXISTS"
                status_color = "#DC3545"
            else:
                status_text = f"❓ Status: {status}"
                status_color = "#FFC107"
            
            tk.Label(root, text=status_text, 
                    font=("Arial", 16, "bold"), fg=status_color).pack(pady=10)
            
            # Details
            if results.get("details"):
                details_frame = tk.LabelFrame(root, text="Details Found", 
                                            font=("Arial", 12, "bold"), padx=20, pady=10)
                details_frame.pack(pady=20, padx=20, fill="x")
                
                for detail in results["details"]:
                    tk.Label(details_frame, text=f"• {detail}", 
                            font=("Arial", 12), anchor="w").pack(fill="x", pady=2)
            else:
                tk.Label(root, text="⚠️ No additional details found", 
                        font=("Arial", 12), fg="#FFC107").pack(pady=10)
                
                tk.Label(root, text="Check these files for manual inspection:", 
                        font=("Arial", 10)).pack(pady=5)
                tk.Label(root, text="• results_page_json.html", 
                        font=("Arial", 10), fg="#666").pack()
                tk.Label(root, text="• all_scripts_content.txt", 
                        font=("Arial", 10), fg="#666").pack()
            
            tk.Button(root, text="Close", command=root.destroy,
                     font=("Arial", 12, "bold"), bg="#6C757D", fg="white",
                     padx=30, pady=10).pack(pady=20)
            
            root.mainloop()
            
        except Exception as e:
            logger.error(f"Error displaying results: {e}")
    
    def run(self):
        """Main execution"""
        try:
            logger.info("🚀 Starting JSON Aadhaar Validator")
            
            if not self.setup_driver():
                return
            
            if not self.complete_validation_steps():
                return
            
            results = self.extract_json_data()
            self.display_results(results)
            
            logger.info("🎉 JSON validation completed!")
            
        except Exception as e:
            logger.error(f"Validation failed: {e}")
        finally:
            if self.driver:
                self.driver.quit()

if __name__ == "__main__":
    validator = JsonAadhaarValidator()
    validator.run()
