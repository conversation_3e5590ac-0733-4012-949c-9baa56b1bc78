#!/usr/bin/env python3
"""
Final Working Aadhaar Validation Script
Addresses the "invalid element state" issue with robust solutions
"""

import time
import base64
import io
import tkinter as tk
from tkinter import messagebox, simpledialog
from PIL import Image, ImageTk
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalAadhaarValidator:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.target_url = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
        
    def setup_driver(self):
        """Setup Chrome WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 15)
            logger.info("WebDriver initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize WebDriver: {e}")
            return False
    
    def get_aadhaar_number(self):
        """Get Aadhaar number from user"""
        root = tk.Tk()
        root.withdraw()
        
        while True:
            aadhaar = simpledialog.askstring(
                "Aadhaar Validation", 
                "Enter your 12-digit Aadhaar number:",
                parent=root
            )
            
            if aadhaar is None:
                root.destroy()
                return None
                
            aadhaar = aadhaar.replace(" ", "").replace("-", "")
            if len(aadhaar) == 12 and aadhaar.isdigit():
                root.destroy()
                return aadhaar
            else:
                messagebox.showerror("Invalid Input", "Please enter a valid 12-digit Aadhaar number")
    
    def navigate_and_wait(self):
        """Navigate to website and wait for it to load"""
        try:
            logger.info(f"Navigating to {self.target_url}")
            self.driver.get(self.target_url)
            
            # Wait for page to load completely
            self.wait.until(lambda driver: driver.execute_script("return document.readyState") == "complete")
            time.sleep(5)  # Additional wait for dynamic content
            
            logger.info(f"Page loaded. Title: {self.driver.title}")
            return True
        except Exception as e:
            logger.error(f"Navigation failed: {e}")
            return False
    
    def find_and_fill_aadhaar(self, aadhaar_number):
        """Find input field and fill Aadhaar number using multiple strategies"""
        try:
            # Strategy 1: Find any text input that could be the Aadhaar field
            input_elements = self.driver.find_elements(By.TAG_NAME, "input")
            logger.info(f"Found {len(input_elements)} input elements")
            
            target_input = None
            for i, inp in enumerate(input_elements):
                try:
                    input_type = inp.get_attribute("type") or ""
                    input_class = inp.get_attribute("class") or ""
                    input_maxlength = inp.get_attribute("maxlength") or ""
                    is_displayed = inp.is_displayed()
                    is_enabled = inp.is_enabled()
                    
                    logger.info(f"Input {i+1}: type={input_type}, class={input_class[:30]}, "
                              f"maxlength={input_maxlength}, displayed={is_displayed}, enabled={is_enabled}")
                    
                    # Look for likely Aadhaar input field
                    if (is_displayed and is_enabled and 
                        (input_type in ["text", "number", ""] or 
                         "text-field" in input_class.lower() or
                         input_maxlength == "12")):
                        target_input = inp
                        logger.info(f"Selected input {i+1} as target field")
                        break
                        
                except Exception as e:
                    logger.warning(f"Error checking input {i+1}: {e}")
            
            if not target_input:
                logger.error("No suitable input field found")
                return False
            
            # Strategy 2: Multiple filling approaches
            return self.fill_input_field(target_input, aadhaar_number)
            
        except Exception as e:
            logger.error(f"Error in find_and_fill_aadhaar: {e}")
            return False
    
    def fill_input_field(self, element, text):
        """Fill input field using multiple methods"""
        methods = [
            ("JavaScript Direct", self._fill_javascript_direct),
            ("JavaScript with Events", self._fill_javascript_events),
            ("Click and Type", self._fill_click_type),
            ("Focus and Type", self._fill_focus_type),
            ("Clear and Type", self._fill_clear_type)
        ]
        
        for method_name, method_func in methods:
            try:
                logger.info(f"Trying method: {method_name}")
                
                # Scroll to element first
                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                time.sleep(1)
                
                if method_func(element, text):
                    # Verify the text was entered
                    current_value = element.get_attribute("value") or ""
                    if current_value == text:
                        logger.info(f"✅ Successfully filled using {method_name}")
                        return True
                    else:
                        logger.warning(f"Method {method_name} didn't set correct value. Got: {current_value}")
                        
            except Exception as e:
                logger.warning(f"Method {method_name} failed: {e}")
                continue
        
        logger.error("All filling methods failed")
        return False
    
    def _fill_javascript_direct(self, element, text):
        """Fill using direct JavaScript value assignment"""
        self.driver.execute_script("arguments[0].value = arguments[1];", element, text)
        return True
    
    def _fill_javascript_events(self, element, text):
        """Fill using JavaScript with proper events"""
        script = """
        var element = arguments[0];
        var text = arguments[1];
        element.focus();
        element.value = '';
        element.value = text;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        """
        self.driver.execute_script(script, element, text)
        return True
    
    def _fill_click_type(self, element, text):
        """Fill by clicking and typing"""
        element.click()
        time.sleep(0.5)
        element.send_keys(Keys.CONTROL + "a")
        time.sleep(0.2)
        element.send_keys(text)
        return True
    
    def _fill_focus_type(self, element, text):
        """Fill by focusing and typing"""
        self.driver.execute_script("arguments[0].focus();", element)
        time.sleep(0.5)
        element.send_keys(Keys.CONTROL + "a")
        time.sleep(0.2)
        element.send_keys(text)
        return True
    
    def _fill_clear_type(self, element, text):
        """Fill by clearing and typing"""
        element.clear()
        time.sleep(0.5)
        element.send_keys(text)
        return True
    
    def extract_captcha_image(self):
        """Extract captcha image from the website"""
        try:
            # Try multiple selectors for captcha container
            captcha_selectors = [
                (By.CLASS_NAME, "auth-form__captcha-box"),
                (By.CSS_SELECTOR, ".auth-form__captcha-box"),
                (By.XPATH, "//div[contains(@class, 'captcha-box')]"),
                (By.XPATH, "//div[contains(@class, 'captcha')]//img"),
                (By.TAG_NAME, "img")
            ]

            captcha_img = None
            for selector in captcha_selectors:
                try:
                    if selector[0] == By.TAG_NAME:
                        # For img tag, find all and look for captcha-like images
                        images = self.driver.find_elements(*selector)
                        for img in images:
                            src = img.get_attribute("src") or ""
                            if "data:image" in src or "captcha" in src.lower():
                                captcha_img = img
                                logger.info(f"Found captcha image using {selector}")
                                break
                    else:
                        # For container selectors, find the container then the img inside
                        container = self.driver.find_element(*selector)
                        captcha_img = container.find_element(By.TAG_NAME, "img")
                        logger.info(f"Found captcha container using {selector}")
                        break
                except:
                    continue

            if not captcha_img:
                logger.error("Could not find captcha image element")
                return None

            # Get the image source
            img_src = captcha_img.get_attribute("src")
            if not img_src:
                logger.error("Captcha image has no src attribute")
                return None

            logger.info(f"Captcha image src: {img_src[:100]}...")

            # Handle base64 images
            if img_src.startswith("data:image"):
                try:
                    # Extract base64 data
                    base64_data = img_src.split(",")[1]
                    image_data = base64.b64decode(base64_data)
                    image = Image.open(io.BytesIO(image_data))
                    logger.info("Successfully extracted base64 captcha image")
                    return image
                except Exception as e:
                    logger.error(f"Error processing base64 image: {e}")
                    return None
            else:
                logger.error("Captcha image is not in base64 format")
                return None

        except Exception as e:
            logger.error(f"Error extracting captcha image: {e}")
            return None

    def solve_captcha_gui(self, captcha_image):
        """Display captcha in GUI and get user input"""
        if captcha_image is None:
            return None

        root = tk.Tk()
        root.title("Captcha Solver - Enter the text you see")
        root.geometry("500x400")
        root.resizable(False, False)

        # Center the window
        root.eval('tk::PlaceWindow . center')

        # Title
        title_label = tk.Label(root, text="🔐 Captcha Verification",
                              font=("Arial", 16, "bold"), fg="#2E86AB")
        title_label.pack(pady=10)

        # Display captcha image (resize for better visibility)
        try:
            # Resize image for better visibility
            display_image = captcha_image.resize((300, 150), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(display_image)

            img_frame = tk.Frame(root, relief="solid", bd=2)
            img_frame.pack(pady=10)

            img_label = tk.Label(img_frame, image=photo, bg="white")
            img_label.pack(padx=10, pady=10)

        except Exception as e:
            logger.error(f"Error displaying captcha image: {e}")
            tk.Label(root, text="❌ Error displaying captcha image",
                    font=("Arial", 12), fg="red").pack(pady=10)
            root.destroy()
            return None

        # Instructions
        instruction_label = tk.Label(root, text="Enter the text shown in the captcha image above:",
                                   font=("Arial", 12), fg="#333")
        instruction_label.pack(pady=(10, 5))

        # Input field for captcha
        input_frame = tk.Frame(root)
        input_frame.pack(pady=10)

        captcha_entry = tk.Entry(input_frame, font=("Arial", 16), width=15,
                               justify="center", relief="solid", bd=2)
        captcha_entry.pack(pady=5)
        captcha_entry.focus()

        # Result variable
        captcha_text = None

        def submit_captcha():
            nonlocal captcha_text
            text = captcha_entry.get().strip()
            if text:
                captcha_text = text
                root.destroy()
            else:
                messagebox.showerror("Error", "Please enter the captcha text")
                captcha_entry.focus()

        def on_enter(event):
            submit_captcha()

        captcha_entry.bind('<Return>', on_enter)

        # Buttons
        button_frame = tk.Frame(root)
        button_frame.pack(pady=20)

        submit_btn = tk.Button(button_frame, text="✓ Submit", command=submit_captcha,
                              font=("Arial", 12, "bold"), bg="#28A745", fg="white",
                              padx=20, pady=5, relief="raised", bd=2)
        submit_btn.pack(side=tk.LEFT, padx=10)

        cancel_btn = tk.Button(button_frame, text="✗ Cancel", command=root.destroy,
                              font=("Arial", 12), bg="#DC3545", fg="white",
                              padx=20, pady=5, relief="raised", bd=2)
        cancel_btn.pack(side=tk.LEFT, padx=10)

        # Status label
        status_label = tk.Label(root, text="💡 Tip: Look carefully at each character",
                              font=("Arial", 10), fg="#666")
        status_label.pack(pady=5)

        root.mainloop()
        return captcha_text

    def fill_captcha_field(self, captcha_text):
        """Fill the captcha field in the website"""
        try:
            # Try multiple selectors for captcha input field
            captcha_selectors = [
                (By.CLASS_NAME, "auth-form__captcha-field"),
                (By.CSS_SELECTOR, ".auth-form__captcha-field"),
                (By.XPATH, "//input[contains(@class, 'captcha-field')]"),
                (By.XPATH, "//input[contains(@placeholder, 'captcha') or contains(@placeholder, 'Captcha')]"),
                (By.CSS_SELECTOR, "input[type='text']:not([maxlength='12'])")  # Text input that's not Aadhaar
            ]

            captcha_field = None
            for selector in captcha_selectors:
                try:
                    elements = self.driver.find_elements(*selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            # Check if this is likely the captcha field (not the Aadhaar field)
                            current_value = element.get_attribute("value") or ""
                            maxlength = element.get_attribute("maxlength") or ""

                            # Skip if it already has the Aadhaar number or has maxlength=12
                            if len(current_value) == 12 or maxlength == "12":
                                continue

                            captcha_field = element
                            logger.info(f"Found captcha field using {selector}")
                            break
                    if captcha_field:
                        break
                except:
                    continue

            if not captcha_field:
                logger.error("Could not find captcha input field")
                return False

            # Fill the captcha using multiple strategies
            return self.fill_input_field(captcha_field, captcha_text)

        except Exception as e:
            logger.error(f"Error filling captcha field: {e}")
            return False

    def click_proceed_button(self):
        """Click the Proceed button"""
        try:
            # Try multiple selectors for the proceed button
            button_selectors = [
                (By.CSS_SELECTOR, ".auth-form__button-container .auth-form__button"),
                (By.CLASS_NAME, "auth-form__button"),
                (By.XPATH, "//button[contains(text(), 'Proceed') or contains(text(), 'proceed')]"),
                (By.XPATH, "//input[@type='submit']"),
                (By.XPATH, "//button[@type='submit']"),
                (By.CSS_SELECTOR, "button[type='submit']"),
                (By.TAG_NAME, "button")
            ]

            proceed_button = None
            for selector in button_selectors:
                try:
                    elements = self.driver.find_elements(*selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            button_text = element.text.lower()
                            button_type = element.get_attribute("type") or ""

                            # Look for proceed button or submit button
                            if ("proceed" in button_text or
                                button_type == "submit" or
                                "submit" in button_text):
                                proceed_button = element
                                logger.info(f"Found proceed button using {selector}")
                                break
                    if proceed_button:
                        break
                except:
                    continue

            if not proceed_button:
                logger.error("Could not find proceed button")
                return False

            # Scroll to button and click
            self.driver.execute_script("arguments[0].scrollIntoView(true);", proceed_button)
            time.sleep(1)

            try:
                proceed_button.click()
                logger.info("Successfully clicked proceed button")
                return True
            except:
                # Try JavaScript click as fallback
                self.driver.execute_script("arguments[0].click();", proceed_button)
                logger.info("Successfully clicked proceed button using JavaScript")
                return True

        except Exception as e:
            logger.error(f"Error clicking proceed button: {e}")
            return False

    def check_submission_success(self):
        """Check if form submission was successful"""
        try:
            # Wait a moment for page transition
            time.sleep(3)

            # Method 1: Check if form elements are gone (moved to results page)
            try:
                form_elements = self.driver.find_elements(By.CLASS_NAME, "auth-form__text-field")
                captcha_elements = self.driver.find_elements(By.CLASS_NAME, "auth-form__captcha-field")

                if not form_elements and not captcha_elements:
                    logger.info("Form elements not found - likely moved to results page")
                    return True
            except:
                pass

            # Method 2: Check for success indicators in page content
            page_source = self.driver.page_source.lower()
            success_indicators = [
                "aadhaar number exists",
                "exists",
                "valid",
                "age band",
                "gender",
                "state"
            ]

            for indicator in success_indicators:
                if indicator in page_source:
                    logger.info(f"Found success indicator: {indicator}")
                    return True

            # Method 3: Check URL change
            current_url = self.driver.current_url
            if "result" in current_url.lower() or "success" in current_url.lower():
                logger.info("URL indicates success page")
                return True

            logger.warning("Could not confirm successful submission")
            return False

        except Exception as e:
            logger.error(f"Error checking submission success: {e}")
            return False

    def extract_validation_results(self):
        """Extract validation results from the results page"""
        try:
            results = {
                "status": "UNKNOWN",
                "aadhaar_number": "",
                "age_band": "",
                "gender": "",
                "state": "",
                "mobile": ""
            }

            page_source = self.driver.page_source
            page_text = page_source.lower()

            # Check if Aadhaar exists
            if any(word in page_text for word in ["exists", "valid", "found"]):
                results["status"] = "EXISTS"
            elif any(word in page_text for word in ["not exist", "invalid", "not found"]):
                results["status"] = "NOT_EXISTS"

            # Try to extract specific information using various methods
            try:
                # Look for text patterns in the page
                import re

                # Extract age band
                age_match = re.search(r'age\s*band[:\s]*(\d+-\d+\s*years?)', page_text, re.IGNORECASE)
                if age_match:
                    results["age_band"] = age_match.group(1)

                # Extract gender
                gender_match = re.search(r'gender[:\s]*(male|female)', page_text, re.IGNORECASE)
                if gender_match:
                    results["gender"] = gender_match.group(1).upper()

                # Extract state
                state_match = re.search(r'state[:\s]*([a-zA-Z\s]+)', page_text, re.IGNORECASE)
                if state_match:
                    results["state"] = state_match.group(1).strip()

                # Extract mobile (masked)
                mobile_match = re.search(r'mobile[:\s]*(\*+\d+)', page_text, re.IGNORECASE)
                if mobile_match:
                    results["mobile"] = mobile_match.group(1)

            except Exception as e:
                logger.warning(f"Error extracting detailed results: {e}")

            logger.info(f"Extracted results: {results}")
            return results

        except Exception as e:
            logger.error(f"Error extracting validation results: {e}")
            return {"status": "ERROR", "error": str(e)}

    def display_results(self, results):
        """Display validation results to user"""
        try:
            root = tk.Tk()
            root.title("Aadhaar Validation Results")
            root.geometry("500x400")
            root.resizable(False, False)
            root.eval('tk::PlaceWindow . center')

            # Title
            title_label = tk.Label(root, text="🎯 Aadhaar Validation Results",
                                  font=("Arial", 18, "bold"), fg="#2E86AB")
            title_label.pack(pady=20)

            # Status
            status = results.get("status", "UNKNOWN")
            if status == "EXISTS":
                status_text = "✅ Aadhaar Number EXISTS"
                status_color = "#28A745"
            elif status == "NOT_EXISTS":
                status_text = "❌ Aadhaar Number NOT EXISTS"
                status_color = "#DC3545"
            else:
                status_text = f"❓ Status: {status}"
                status_color = "#FFC107"

            status_label = tk.Label(root, text=status_text,
                                   font=("Arial", 16, "bold"), fg=status_color)
            status_label.pack(pady=10)

            # Details frame
            if any(results.get(key) for key in ["age_band", "gender", "state", "mobile"]):
                details_frame = tk.LabelFrame(root, text="Details", font=("Arial", 12, "bold"),
                                            padx=20, pady=10)
                details_frame.pack(pady=20, padx=20, fill="x")

                details = [
                    ("Age Band", results.get("age_band", "")),
                    ("Gender", results.get("gender", "")),
                    ("State", results.get("state", "")),
                    ("Mobile", results.get("mobile", ""))
                ]

                for label, value in details:
                    if value:
                        detail_label = tk.Label(details_frame,
                                              text=f"{label}: {value}",
                                              font=("Arial", 12), anchor="w")
                        detail_label.pack(fill="x", pady=2)

            # Close button
            close_btn = tk.Button(root, text="Close", command=root.destroy,
                                 font=("Arial", 12, "bold"), bg="#6C757D", fg="white",
                                 padx=30, pady=10, relief="raised", bd=2)
            close_btn.pack(pady=20)

            root.mainloop()

        except Exception as e:
            logger.error(f"Error displaying results: {e}")
            messagebox.showinfo("Results", f"Validation completed. Status: {results.get('status', 'Unknown')}")

    def debug_page_after_aadhaar(self):
        """Debug page structure after Aadhaar is filled"""
        try:
            logger.info("=== DEBUGGING PAGE AFTER AADHAAR FILLED ===")

            # Look for images (potential captcha)
            images = self.driver.find_elements(By.TAG_NAME, "img")
            logger.info(f"Found {len(images)} image elements:")

            for i, img in enumerate(images):
                try:
                    src = img.get_attribute("src") or ""
                    img_class = img.get_attribute("class") or ""
                    is_displayed = img.is_displayed()

                    logger.info(f"Image {i+1}: class={img_class}, displayed={is_displayed}")
                    if "data:image" in src:
                        logger.info(f"  *** FOUND BASE64 IMAGE ***")

                except Exception as e:
                    logger.warning(f"Error checking image {i+1}: {e}")

            # Look for all input fields
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            logger.info(f"Found {len(inputs)} input elements:")

            for i, inp in enumerate(inputs):
                try:
                    input_type = inp.get_attribute("type") or ""
                    input_class = inp.get_attribute("class") or ""
                    input_placeholder = inp.get_attribute("placeholder") or ""
                    is_displayed = inp.is_displayed()
                    is_enabled = inp.is_enabled()

                    logger.info(f"Input {i+1}: type={input_type}, class={input_class[:50]}, placeholder='{input_placeholder}', displayed={is_displayed}, enabled={is_enabled}")

                except Exception as e:
                    logger.warning(f"Error checking input {i+1}: {e}")

            # Look for buttons
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            logger.info(f"Found {len(buttons)} button elements:")

            for i, btn in enumerate(buttons):
                try:
                    btn_text = btn.text or ""
                    btn_class = btn.get_attribute("class") or ""
                    is_displayed = btn.is_displayed()
                    is_enabled = btn.is_enabled()

                    logger.info(f"Button {i+1}: text='{btn_text}', class={btn_class[:50]}, displayed={is_displayed}, enabled={is_enabled}")

                except Exception as e:
                    logger.warning(f"Error checking button {i+1}: {e}")

            # Save page source
            try:
                with open("page_debug.html", "w", encoding="utf-8") as f:
                    f.write(self.driver.page_source)
                logger.info("Page source saved to page_debug.html")
            except Exception as e:
                logger.error(f"Error saving page source: {e}")

            logger.info("=== DEBUG COMPLETE ===")

        except Exception as e:
            logger.error(f"Error in debug_page_after_aadhaar: {e}")
    
    def run(self):
        """Main execution with correct flow"""
        try:
            logger.info("🚀 Starting Aadhaar Validator - Correct Flow")

            # Step 1: Setup WebDriver
            if not self.setup_driver():
                messagebox.showerror("Error", "Failed to initialize browser")
                return False

            # Step 2: Navigate to website FIRST
            logger.info("Step 1: Loading UIDAI website...")
            if not self.navigate_and_wait():
                messagebox.showerror("Error", "Failed to load the UIDAI website")
                return False

            # Step 3: NOW ask for Aadhaar number (after site is loaded)
            logger.info("Step 2: Website loaded, asking for Aadhaar number...")
            aadhaar_number = self.get_aadhaar_number()
            if not aadhaar_number:
                logger.info("User cancelled")
                return False

            # Step 4: Fill Aadhaar number
            logger.info("Step 3: Filling Aadhaar number...")
            if not self.find_and_fill_aadhaar(aadhaar_number):
                messagebox.showerror("Error", "Could not fill the Aadhaar number")
                return False

            # Step 5: Wait for captcha to appear (it might load dynamically)
            logger.info("Step 4: Waiting for captcha to appear...")
            time.sleep(3)  # Wait for dynamic content

            # Step 5: Extract captcha image
            logger.info("Step 5: Extracting captcha image...")
            captcha_image = self.extract_captcha_image()
            if not captcha_image:
                logger.warning("Could not extract captcha image, checking page structure...")
                self.debug_page_after_aadhaar()
                messagebox.showerror("Error",
                    "Could not extract captcha image. The website structure may have changed.\n"
                    "Check the browser window and page_debug.html file for details.")
                return False

            # Step 6: Show captcha in GUI and get user input
            logger.info("Step 5: Showing captcha GUI...")
            captcha_text = self.solve_captcha_gui(captcha_image)
            if not captcha_text:
                logger.info("User cancelled captcha input")
                return False

            # Step 7: Fill captcha in website
            logger.info("Step 6: Filling captcha in website...")
            if not self.fill_captcha_field(captcha_text):
                messagebox.showerror("Error", "Could not fill captcha")
                return False

            # Step 8: Automatically click Proceed
            logger.info("Step 7: Clicking Proceed button...")
            if not self.click_proceed_button():
                messagebox.showerror("Error", "Could not click Proceed button")
                return False

            # Step 9: Wait for results and extract them
            logger.info("Step 8: Waiting for results...")
            time.sleep(5)  # Wait for page to process

            if self.check_submission_success():
                results = self.extract_validation_results()
                self.display_results(results)
            else:
                messagebox.showerror("Error", "Form submission may have failed")

            return True

        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            messagebox.showerror("Error", f"An unexpected error occurred: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()
                logger.info("Browser closed")

if __name__ == "__main__":
    validator = FinalAadhaarValidator()
    validator.run()
