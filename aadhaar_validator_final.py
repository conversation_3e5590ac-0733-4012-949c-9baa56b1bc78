#!/usr/bin/env python3
"""
Final Working Aadhaar Validation Script
Addresses the "invalid element state" issue with robust solutions
"""

import time
import tkinter as tk
from tkinter import messagebox, simpledialog
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalAadhaarValidator:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.target_url = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
        
    def setup_driver(self):
        """Setup Chrome WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 15)
            logger.info("WebDriver initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize WebDriver: {e}")
            return False
    
    def get_aadhaar_number(self):
        """Get Aadhaar number from user"""
        root = tk.Tk()
        root.withdraw()
        
        while True:
            aadhaar = simpledialog.askstring(
                "Aadhaar Validation", 
                "Enter your 12-digit Aadhaar number:",
                parent=root
            )
            
            if aadhaar is None:
                root.destroy()
                return None
                
            aadhaar = aadhaar.replace(" ", "").replace("-", "")
            if len(aadhaar) == 12 and aadhaar.isdigit():
                root.destroy()
                return aadhaar
            else:
                messagebox.showerror("Invalid Input", "Please enter a valid 12-digit Aadhaar number")
    
    def navigate_and_wait(self):
        """Navigate to website and wait for it to load"""
        try:
            logger.info(f"Navigating to {self.target_url}")
            self.driver.get(self.target_url)
            
            # Wait for page to load completely
            self.wait.until(lambda driver: driver.execute_script("return document.readyState") == "complete")
            time.sleep(5)  # Additional wait for dynamic content
            
            logger.info(f"Page loaded. Title: {self.driver.title}")
            return True
        except Exception as e:
            logger.error(f"Navigation failed: {e}")
            return False
    
    def find_and_fill_aadhaar(self, aadhaar_number):
        """Find input field and fill Aadhaar number using multiple strategies"""
        try:
            # Strategy 1: Find any text input that could be the Aadhaar field
            input_elements = self.driver.find_elements(By.TAG_NAME, "input")
            logger.info(f"Found {len(input_elements)} input elements")
            
            target_input = None
            for i, inp in enumerate(input_elements):
                try:
                    input_type = inp.get_attribute("type") or ""
                    input_class = inp.get_attribute("class") or ""
                    input_maxlength = inp.get_attribute("maxlength") or ""
                    is_displayed = inp.is_displayed()
                    is_enabled = inp.is_enabled()
                    
                    logger.info(f"Input {i+1}: type={input_type}, class={input_class[:30]}, "
                              f"maxlength={input_maxlength}, displayed={is_displayed}, enabled={is_enabled}")
                    
                    # Look for likely Aadhaar input field
                    if (is_displayed and is_enabled and 
                        (input_type in ["text", "number", ""] or 
                         "text-field" in input_class.lower() or
                         input_maxlength == "12")):
                        target_input = inp
                        logger.info(f"Selected input {i+1} as target field")
                        break
                        
                except Exception as e:
                    logger.warning(f"Error checking input {i+1}: {e}")
            
            if not target_input:
                logger.error("No suitable input field found")
                return False
            
            # Strategy 2: Multiple filling approaches
            return self.fill_input_field(target_input, aadhaar_number)
            
        except Exception as e:
            logger.error(f"Error in find_and_fill_aadhaar: {e}")
            return False
    
    def fill_input_field(self, element, text):
        """Fill input field using multiple methods"""
        methods = [
            ("JavaScript Direct", self._fill_javascript_direct),
            ("JavaScript with Events", self._fill_javascript_events),
            ("Click and Type", self._fill_click_type),
            ("Focus and Type", self._fill_focus_type),
            ("Clear and Type", self._fill_clear_type)
        ]
        
        for method_name, method_func in methods:
            try:
                logger.info(f"Trying method: {method_name}")
                
                # Scroll to element first
                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                time.sleep(1)
                
                if method_func(element, text):
                    # Verify the text was entered
                    current_value = element.get_attribute("value") or ""
                    if current_value == text:
                        logger.info(f"✅ Successfully filled using {method_name}")
                        return True
                    else:
                        logger.warning(f"Method {method_name} didn't set correct value. Got: {current_value}")
                        
            except Exception as e:
                logger.warning(f"Method {method_name} failed: {e}")
                continue
        
        logger.error("All filling methods failed")
        return False
    
    def _fill_javascript_direct(self, element, text):
        """Fill using direct JavaScript value assignment"""
        self.driver.execute_script("arguments[0].value = arguments[1];", element, text)
        return True
    
    def _fill_javascript_events(self, element, text):
        """Fill using JavaScript with proper events"""
        script = """
        var element = arguments[0];
        var text = arguments[1];
        element.focus();
        element.value = '';
        element.value = text;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        """
        self.driver.execute_script(script, element, text)
        return True
    
    def _fill_click_type(self, element, text):
        """Fill by clicking and typing"""
        element.click()
        time.sleep(0.5)
        element.send_keys(Keys.CONTROL + "a")
        time.sleep(0.2)
        element.send_keys(text)
        return True
    
    def _fill_focus_type(self, element, text):
        """Fill by focusing and typing"""
        self.driver.execute_script("arguments[0].focus();", element)
        time.sleep(0.5)
        element.send_keys(Keys.CONTROL + "a")
        time.sleep(0.2)
        element.send_keys(text)
        return True
    
    def _fill_clear_type(self, element, text):
        """Fill by clearing and typing"""
        element.clear()
        time.sleep(0.5)
        element.send_keys(text)
        return True
    
    def show_success_message(self, aadhaar_number):
        """Show success message and keep browser open"""
        message = f"""✅ SUCCESS!

Aadhaar number {aadhaar_number} has been filled successfully!

Next steps:
1. The browser window is now open with your Aadhaar number filled
2. Solve the captcha manually
3. Click the "Proceed" button
4. View your validation results

The browser will remain open for you to complete the process.
Click OK to continue..."""
        
        messagebox.showinfo("Success - Manual Completion Required", message)
    
    def run(self):
        """Main execution"""
        try:
            logger.info("🚀 Starting Final Aadhaar Validator")
            
            # Setup
            if not self.setup_driver():
                messagebox.showerror("Error", "Failed to initialize browser")
                return False
            
            # Get Aadhaar number
            aadhaar_number = self.get_aadhaar_number()
            if not aadhaar_number:
                logger.info("User cancelled")
                return False
            
            # Navigate to website
            if not self.navigate_and_wait():
                messagebox.showerror("Error", "Failed to load the UIDAI website")
                return False
            
            # Fill Aadhaar number
            if not self.find_and_fill_aadhaar(aadhaar_number):
                messagebox.showerror("Error", 
                    "Could not fill the Aadhaar number automatically.\n\n"
                    "The browser is open - please fill it manually:\n"
                    f"Aadhaar Number: {aadhaar_number}")
            else:
                self.show_success_message(aadhaar_number)
            
            # Keep browser open for manual completion
            input("\nPress Enter to close the browser and exit...")
            
            return True
            
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            messagebox.showerror("Error", f"An unexpected error occurred: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()
                logger.info("Browser closed")

if __name__ == "__main__":
    validator = FinalAadhaarValidator()
    validator.run()
