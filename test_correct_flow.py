#!/usr/bin/env python3
"""
Test script to verify the correct flow
"""

import time
import tkinter as tk
from tkinter import messagebox, simpledialog
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def test_correct_flow():
    """Test the correct flow step by step"""
    
    print("🧪 Testing Correct Flow")
    print("=" * 50)
    
    # Step 1: Setup WebDriver
    print("Step 1: Setting up WebDriver...")
    try:
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        print("✅ WebDriver setup successful")
    except Exception as e:
        print(f"❌ WebDriver setup failed: {e}")
        return False
    
    try:
        # Step 2: Navigate to website FIRST
        print("\nStep 2: Loading UIDAI website...")
        url = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
        driver.get(url)
        time.sleep(5)
        print(f"✅ Website loaded: {driver.title}")
        
        # Step 3: NOW ask for Aadhaar number (after site is loaded)
        print("\nStep 3: Website is loaded, now asking for Aadhaar number...")
        
        root = tk.Tk()
        root.withdraw()
        
        aadhaar = simpledialog.askstring(
            "Aadhaar Validation", 
            "Website is loaded! Enter your 12-digit Aadhaar number:",
            parent=root
        )
        root.destroy()
        
        if not aadhaar:
            print("❌ User cancelled")
            return False
        
        print(f"✅ Got Aadhaar number: {aadhaar}")
        
        # Step 4: Show that we would fill the form
        print("\nStep 4: Would fill Aadhaar number in form...")
        print("Step 5: Would extract captcha image...")
        print("Step 6: Would show captcha GUI...")
        print("Step 7: Would fill captcha in website...")
        print("Step 8: Would click Proceed button...")
        print("Step 9: Would extract results...")
        
        print("\n✅ Flow test completed successfully!")
        print("The correct order is:")
        print("1. Load website FIRST")
        print("2. THEN ask for Aadhaar number")
        print("3. Fill Aadhaar")
        print("4. Extract captcha")
        print("5. Show captcha GUI")
        print("6. Fill captcha")
        print("7. Click Proceed")
        print("8. Extract results")
        
        # Keep browser open for inspection
        input("\nPress Enter to close browser...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in flow test: {e}")
        return False
    finally:
        driver.quit()

if __name__ == "__main__":
    test_correct_flow()
