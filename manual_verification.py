#!/usr/bin/env python3
"""
Manual Verification - Let user fill manually and then extract results
"""

import time
import tkinter as tk
from tkinter import messagebox
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def manual_verification():
    """Let user complete manually, then extract results"""
    
    print("🔍 Manual Verification Test")
    
    # Setup
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        # Load website
        print("Loading website...")
        driver.get("https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en")
        time.sleep(5)
        
        print("✅ Website loaded")
        
        # Instructions for manual completion
        root = tk.Tk()
        root.withdraw()
        messagebox.showinfo("Manual Instructions", 
            "🔍 Manual Verification Test\n\n"
            "Please complete the following steps MANUALLY in the browser:\n\n"
            "1. Enter your Aadhaar number\n"
            "2. Solve the captcha\n"
            "3. Click the Proceed button\n"
            "4. Wait for the results page to load\n\n"
            "Once you see the results, click OK here to continue...")
        root.destroy()
        
        print("User completed manual validation")
        
        # Now extract results using the exact structure
        print("🔍 Extracting results from card structure...")
        
        try:
            # Find the response card
            response_card = driver.find_element(By.CSS_SELECTOR, 
                ".check-aadhaar-validity-response__card")
            
            print("✅ Found response card!")
            
            # Find all verify-display-field divs
            field_divs = response_card.find_elements(By.CSS_SELECTOR, 
                ".verify-display-field")
            
            print(f"Found {len(field_divs)} field divs")
            
            results = []
            
            for i, field_div in enumerate(field_divs):
                try:
                    # Get the label
                    label_span = field_div.find_element(By.CSS_SELECTOR, 
                        ".verify-display-field__label")
                    
                    # Get all spans
                    all_spans = field_div.find_elements(By.TAG_NAME, "span")
                    
                    label_text = label_span.text.strip()
                    value_text = ""
                    
                    # Find the value span (not the label)
                    for span in all_spans:
                        span_text = span.text.strip()
                        if span_text and span_text != label_text:
                            value_text = span_text
                            break
                    
                    if label_text and value_text:
                        result = f"{label_text}: {value_text}"
                        results.append(result)
                        print(f"📋 {result}")
                    
                except Exception as e:
                    print(f"Error processing field {i+1}: {e}")
                    continue
            
            # Display results
            if results:
                result_text = "✅ EXTRACTED RESULTS:\n\n" + "\n".join(results)
                print(f"\n{result_text}")
                
                root = tk.Tk()
                root.withdraw()
                messagebox.showinfo("Extraction Results", result_text)
                root.destroy()
                
                # Save results
                with open("manual_extraction_results.txt", "w") as f:
                    f.write("Manual Verification Results\n")
                    f.write("=" * 30 + "\n")
                    for result in results:
                        f.write(f"{result}\n")
                
                print("✅ Results saved to manual_extraction_results.txt")
                
            else:
                print("❌ No results extracted")
                
                # Save page source for debugging
                with open("manual_page_source.html", "w", encoding="utf-8") as f:
                    f.write(driver.page_source)
                print("Page source saved to manual_page_source.html")
                
        except Exception as e:
            print(f"❌ Error extracting results: {e}")
            
            # Try fallback - search entire page
            print("🔍 Trying fallback extraction...")
            
            try:
                field_divs = driver.find_elements(By.CSS_SELECTOR, ".verify-display-field")
                print(f"Found {len(field_divs)} field divs on entire page")
                
                for i, field_div in enumerate(field_divs):
                    try:
                        text = field_div.text.strip()
                        if text:
                            print(f"Field {i+1}: {text}")
                    except:
                        continue
                        
            except Exception as e2:
                print(f"Fallback also failed: {e2}")
        
        input("Press Enter to close browser...")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    manual_verification()
