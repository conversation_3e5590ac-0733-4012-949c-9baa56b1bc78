#!/usr/bin/env python3
"""
Enhanced Aadhaar Validation Automation Script - Version 2
Handles various website states and element conditions
"""

import time
import base64
import io
import tkinter as tk
from tkinter import messagebox, simpledialog
from PIL import Image, ImageTk
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementNotInteractableException
from webdriver_manager.chrome import ChromeDriverManager
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedAadhaarValidator:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.target_url = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
        
    def setup_driver(self):
        """Initialize Chrome WebDriver with enhanced settings"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-plugins")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 15)
            logger.info("Enhanced WebDriver initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize WebDriver: {e}")
            return False
    
    def get_aadhaar_number(self):
        """Get Aadhaar number from user input"""
        root = tk.Tk()
        root.withdraw()
        
        while True:
            aadhaar = simpledialog.askstring(
                "Aadhaar Validation", 
                "Enter your 12-digit Aadhaar number:",
                parent=root
            )
            
            if aadhaar is None:
                root.destroy()
                return None
                
            aadhaar = aadhaar.replace(" ", "").replace("-", "")
            if len(aadhaar) == 12 and aadhaar.isdigit():
                root.destroy()
                return aadhaar
            else:
                messagebox.showerror("Invalid Input", "Please enter a valid 12-digit Aadhaar number")
    
    def navigate_to_website(self):
        """Navigate to the Aadhaar validation website with retries"""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                logger.info(f"Navigation attempt {attempt + 1}/{max_retries}")
                logger.info(f"Navigating to {self.target_url}")
                
                self.driver.get(self.target_url)
                
                # Wait for page to load completely
                self.wait.until(lambda driver: driver.execute_script("return document.readyState") == "complete")
                
                # Additional wait for dynamic content
                time.sleep(5)
                
                logger.info(f"Page loaded. Title: {self.driver.title}")
                logger.info(f"Current URL: {self.driver.current_url}")
                
                # Try to find any input field to confirm page loaded
                input_found = False
                selectors_to_try = [
                    (By.TAG_NAME, "input"),
                    (By.CSS_SELECTOR, "input[type='text']"),
                    (By.CSS_SELECTOR, "input[type='number']"),
                    (By.CLASS_NAME, "auth-form__text-field"),
                    (By.XPATH, "//input")
                ]
                
                for selector in selectors_to_try:
                    try:
                        elements = self.driver.find_elements(*selector)
                        if elements:
                            logger.info(f"Found {len(elements)} input elements using {selector}")
                            input_found = True
                            break
                    except:
                        continue
                
                if input_found:
                    logger.info("Website loaded successfully")
                    return True
                else:
                    logger.warning(f"No input fields found on attempt {attempt + 1}")
                    if attempt < max_retries - 1:
                        time.sleep(3)
                        continue
                    
            except Exception as e:
                logger.error(f"Navigation attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(3)
                    continue
        
        logger.error("All navigation attempts failed")
        return False
    
    def find_aadhaar_input_field(self):
        """Find the Aadhaar input field using multiple strategies"""
        
        # Strategy 1: Try the expected class name
        selectors_to_try = [
            (By.CLASS_NAME, "auth-form__text-field"),
            (By.CSS_SELECTOR, ".auth-form__text-field"),
            (By.CSS_SELECTOR, "input[type='text']"),
            (By.CSS_SELECTOR, "input[type='number']"),
            (By.XPATH, "//input[contains(@class, 'text-field')]"),
            (By.XPATH, "//input[contains(@placeholder, 'aadhaar') or contains(@placeholder, 'Aadhaar')]"),
            (By.XPATH, "//input[@maxlength='12']"),
            (By.XPATH, "//input[string-length(@maxlength)>0]"),
            (By.TAG_NAME, "input")
        ]
        
        for i, selector in enumerate(selectors_to_try):
            try:
                elements = self.driver.find_elements(*selector)
                logger.info(f"Selector {i+1} {selector}: Found {len(elements)} elements")
                
                for j, element in enumerate(elements):
                    try:
                        # Check if this looks like an Aadhaar field
                        element_type = element.get_attribute("type") or ""
                        element_class = element.get_attribute("class") or ""
                        element_placeholder = element.get_attribute("placeholder") or ""
                        element_maxlength = element.get_attribute("maxlength") or ""
                        element_name = element.get_attribute("name") or ""
                        
                        is_displayed = element.is_displayed()
                        is_enabled = element.is_enabled()
                        
                        logger.info(f"  Element {j+1}: type={element_type}, class={element_class[:50]}, "
                                  f"placeholder={element_placeholder}, maxlength={element_maxlength}, "
                                  f"name={element_name}, displayed={is_displayed}, enabled={is_enabled}")
                        
                        # If it's displayed and enabled, try to use it
                        if is_displayed and is_enabled:
                            return element
                            
                    except Exception as e:
                        logger.info(f"  Element {j+1}: Error checking attributes - {e}")
                        
            except Exception as e:
                logger.info(f"Selector {i+1} failed: {e}")
        
        return None
    
    def fill_aadhaar_number_enhanced(self, aadhaar_number):
        """Enhanced method to fill Aadhaar number"""
        try:
            # Find the input field
            aadhaar_field = self.find_aadhaar_input_field()
            
            if not aadhaar_field:
                logger.error("Could not find Aadhaar input field")
                return False
            
            logger.info("Found Aadhaar input field, attempting to fill...")
            
            # Scroll to element
            self.driver.execute_script("arguments[0].scrollIntoView(true);", aadhaar_field)
            time.sleep(1)
            
            # Try multiple filling strategies
            strategies = [
                self._fill_strategy_click_and_type,
                self._fill_strategy_javascript,
                self._fill_strategy_action_chains,
                self._fill_strategy_char_by_char
            ]
            
            for i, strategy in enumerate(strategies):
                try:
                    logger.info(f"Trying filling strategy {i+1}")
                    if strategy(aadhaar_field, aadhaar_number):
                        logger.info(f"Successfully filled Aadhaar number using strategy {i+1}")
                        return True
                except Exception as e:
                    logger.warning(f"Strategy {i+1} failed: {e}")
                    continue
            
            logger.error("All filling strategies failed")
            return False
            
        except Exception as e:
            logger.error(f"Error in fill_aadhaar_number_enhanced: {e}")
            return False
    
    def _fill_strategy_click_and_type(self, element, text):
        """Strategy 1: Click and type"""
        element.click()
        time.sleep(0.5)
        element.clear()
        time.sleep(0.5)
        element.send_keys(text)
        return True
    
    def _fill_strategy_javascript(self, element, text):
        """Strategy 2: JavaScript"""
        self.driver.execute_script("arguments[0].focus();", element)
        self.driver.execute_script("arguments[0].value = '';", element)
        self.driver.execute_script("arguments[0].value = arguments[1];", element, text)
        self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", element)
        self.driver.execute_script("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", element)
        return True
    
    def _fill_strategy_action_chains(self, element, text):
        """Strategy 3: Action chains"""
        actions = ActionChains(self.driver)
        actions.click(element).perform()
        time.sleep(0.5)
        actions.key_down(Keys.CONTROL).send_keys('a').key_up(Keys.CONTROL).perform()
        time.sleep(0.5)
        actions.send_keys(text).perform()
        return True
    
    def _fill_strategy_char_by_char(self, element, text):
        """Strategy 4: Character by character"""
        element.click()
        time.sleep(0.5)
        element.clear()
        time.sleep(0.5)
        for char in text:
            element.send_keys(char)
            time.sleep(0.1)
        return True
    
    def run_enhanced(self):
        """Enhanced main execution flow"""
        try:
            logger.info("Starting Enhanced Aadhaar validation automation")
            
            # Setup WebDriver
            if not self.setup_driver():
                return False
            
            # Get Aadhaar number from user
            aadhaar_number = self.get_aadhaar_number()
            if not aadhaar_number:
                logger.info("User cancelled Aadhaar input")
                return False
            
            # Navigate to website
            if not self.navigate_to_website():
                return False
            
            # Fill Aadhaar number
            if not self.fill_aadhaar_number_enhanced(aadhaar_number):
                messagebox.showerror("Error", 
                    "Could not fill the Aadhaar number field. The website structure may have changed.")
                return False
            
            messagebox.showinfo("Success", 
                f"Successfully filled Aadhaar number: {aadhaar_number}\n\n"
                "Please complete the captcha and form submission manually in the browser window.\n"
                "The browser will remain open for you to continue.")
            
            # Keep browser open for manual completion
            input("Press Enter to close the browser...")
            
            return True
            
        except Exception as e:
            logger.error(f"Unexpected error in enhanced execution: {e}")
            messagebox.showerror("Error", f"An unexpected error occurred: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()
                logger.info("WebDriver closed")

if __name__ == "__main__":
    validator = EnhancedAadhaarValidator()
    validator.run_enhanced()
