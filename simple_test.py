#!/usr/bin/env python3
"""
Simple test to check website access
"""

import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import time

def test_website_access():
    """Test if we can access the website"""
    
    url = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
    
    print("🌐 Testing website access...")
    
    # Test 1: HTTP request
    try:
        print("Test 1: HTTP Request")
        response = requests.get(url, timeout=10)
        print(f"Status Code: {response.status_code}")
        print(f"Content Length: {len(response.content)}")
        print("✅ HTTP request successful")
    except Exception as e:
        print(f"❌ HTTP request failed: {e}")
    
    print()
    
    # Test 2: Selenium access
    try:
        print("Test 2: Selenium WebDriver")
        
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        print("WebDriver initialized")
        
        driver.get(url)
        print(f"Navigated to: {driver.current_url}")
        
        time.sleep(5)
        
        print(f"Page title: {driver.title}")
        print(f"Page source length: {len(driver.page_source)}")
        
        # Save page source for inspection
        with open("page_source.html", "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        print("Page source saved to page_source.html")
        
        driver.quit()
        print("✅ Selenium test successful")
        
    except Exception as e:
        print(f"❌ Selenium test failed: {e}")
        if 'driver' in locals():
            driver.quit()

if __name__ == "__main__":
    test_website_access()
