#!/usr/bin/env python3
"""
Analyze the saved results_page.html file to find the data
"""

import re
import json
from bs4 import BeautifulSoup
import os

def analyze_results_html():
    """Analyze the saved HTML file to find where the data is stored"""
    
    print("🔍 Analyzing Results HTML File")
    print("=" * 50)
    
    # Check if the file exists
    html_files = ["results_page.html", "results_page_json.html", "debug_results_full.html"]
    
    html_file = None
    for file in html_files:
        if os.path.exists(file):
            html_file = file
            break
    
    if not html_file:
        print("❌ No results HTML file found!")
        print("Please run one of the validator scripts first to generate the HTML file.")
        return
    
    print(f"📁 Analyzing file: {html_file}")
    
    try:
        # Read the HTML file
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        print(f"✅ File loaded. Size: {len(html_content)} characters")
        
        # Parse with BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 1. Look for visible text containing our keywords
        print(f"\n🔍 SEARCHING FOR VISIBLE TEXT:")
        print("-" * 30)
        
        keywords = ['age', 'gender', 'state', 'mobile', 'male', 'female', 'years', 'band']
        
        # Get all text elements
        all_text = soup.get_text()
        text_lines = [line.strip() for line in all_text.split('\n') if line.strip()]
        
        relevant_lines = []
        for line in text_lines:
            if any(keyword in line.lower() for keyword in keywords):
                relevant_lines.append(line)
        
        print(f"Found {len(relevant_lines)} relevant text lines:")
        for i, line in enumerate(relevant_lines[:10]):  # Show first 10
            print(f"  {i+1}: {line}")
        
        if len(relevant_lines) > 10:
            print(f"  ... and {len(relevant_lines) - 10} more lines")
        
        # 2. Look in script tags
        print(f"\n🔍 SEARCHING SCRIPT TAGS:")
        print("-" * 30)
        
        scripts = soup.find_all('script')
        print(f"Found {len(scripts)} script tags")
        
        relevant_scripts = []
        for i, script in enumerate(scripts):
            script_content = script.string or ""
            if script_content and any(keyword in script_content.lower() for keyword in keywords):
                relevant_scripts.append((i, script_content))
        
        print(f"Found {len(relevant_scripts)} scripts with relevant content:")
        
        for script_num, script_content in relevant_scripts:
            print(f"\n--- SCRIPT {script_num + 1} ---")
            print(f"Length: {len(script_content)} characters")
            
            # Look for JSON-like structures
            json_matches = re.findall(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', script_content)
            print(f"JSON objects found: {len(json_matches)}")
            
            for j, json_str in enumerate(json_matches[:3]):  # Show first 3
                try:
                    json_obj = json.loads(json_str)
                    print(f"  JSON {j+1}: {json_obj}")
                except:
                    print(f"  JSON {j+1}: {json_str[:100]}...")
            
            # Look for key-value patterns
            patterns = {
                "Age": r'age["\s]*[:=]\s*["\']?([^"\';\n,]+)',
                "Gender": r'gender["\s]*[:=]\s*["\']?([^"\';\n,]+)',
                "State": r'state["\s]*[:=]\s*["\']?([^"\';\n,]+)',
                "Mobile": r'mobile["\s]*[:=]\s*["\']?([^"\';\n,]+)'
            }
            
            for field, pattern in patterns.items():
                matches = re.findall(pattern, script_content, re.IGNORECASE)
                if matches:
                    print(f"  {field} matches: {matches}")
            
            # Show a snippet of the script
            print(f"  Script snippet: {script_content[:200]}...")
        
        # 3. Look for data attributes
        print(f"\n🔍 SEARCHING DATA ATTRIBUTES:")
        print("-" * 30)
        
        elements_with_data = soup.find_all(attrs={"data-": True})
        print(f"Found {len(elements_with_data)} elements with data attributes")
        
        for elem in elements_with_data[:5]:  # Show first 5
            attrs = {k: v for k, v in elem.attrs.items() if k.startswith('data-')}
            if attrs:
                print(f"  {elem.name}: {attrs}")
        
        # 4. Look for specific class names or IDs
        print(f"\n🔍 SEARCHING FOR RESULT CONTAINERS:")
        print("-" * 30)
        
        result_selectors = [
            'div[class*="result"]',
            'div[class*="info"]',
            'div[class*="detail"]',
            'div[class*="data"]',
            'table',
            'ul',
            'dl'
        ]
        
        for selector in result_selectors:
            elements = soup.select(selector)
            if elements:
                print(f"Found {len(elements)} elements matching '{selector}':")
                for elem in elements[:3]:  # Show first 3
                    text = elem.get_text().strip()
                    if text and any(keyword in text.lower() for keyword in keywords):
                        print(f"  Relevant: {text[:100]}...")
        
        # 5. Look for hidden inputs or form data
        print(f"\n🔍 SEARCHING HIDDEN INPUTS:")
        print("-" * 30)
        
        hidden_inputs = soup.find_all('input', {'type': 'hidden'})
        print(f"Found {len(hidden_inputs)} hidden inputs")
        
        for inp in hidden_inputs:
            name = inp.get('name', '')
            value = inp.get('value', '')
            if name and value and any(keyword in name.lower() or keyword in value.lower() for keyword in keywords):
                print(f"  {name}: {value}")
        
        # 6. Save analysis results
        analysis_results = {
            "file_analyzed": html_file,
            "file_size": len(html_content),
            "relevant_text_lines": len(relevant_lines),
            "relevant_scripts": len(relevant_scripts),
            "sample_text_lines": relevant_lines[:5],
            "script_analysis": []
        }
        
        for script_num, script_content in relevant_scripts:
            script_analysis = {
                "script_number": script_num + 1,
                "length": len(script_content),
                "json_objects": len(re.findall(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', script_content)),
                "snippet": script_content[:500]
            }
            analysis_results["script_analysis"].append(script_analysis)
        
        with open("html_analysis_results.json", "w", encoding="utf-8") as f:
            json.dump(analysis_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Analysis complete!")
        print(f"📁 Results saved to: html_analysis_results.json")
        
        # Summary
        print(f"\n📊 SUMMARY:")
        print(f"  • Relevant text lines: {len(relevant_lines)}")
        print(f"  • Scripts with data: {len(relevant_scripts)}")
        print(f"  • Total scripts: {len(scripts)}")
        
        if relevant_lines:
            print(f"\n💡 RECOMMENDATION:")
            print(f"  The data appears to be in visible text. Check the relevant_lines above.")
        elif relevant_scripts:
            print(f"\n💡 RECOMMENDATION:")
            print(f"  The data appears to be in JavaScript. Check the script analysis above.")
        else:
            print(f"\n❓ NO DATA FOUND:")
            print(f"  The detailed information may not be present in the HTML,")
            print(f"  or it may be loaded dynamically after the page capture.")
        
    except Exception as e:
        print(f"❌ Error analyzing HTML: {e}")

if __name__ == "__main__":
    analyze_results_html()
