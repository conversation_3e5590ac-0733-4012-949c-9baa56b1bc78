#!/usr/bin/env python3
"""
Debug script to see what happens after <PERSON><PERSON><PERSON><PERSON> is filled
"""

import time
import tkinter as tk
from tkinter import messagebox, simpledialog
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_after_aadhaar():
    """Debug what happens after <PERSON><PERSON><PERSON><PERSON> is filled"""
    
    # Setup WebDriver
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        # Navigate to website
        url = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
        logger.info(f"Navigating to {url}")
        driver.get(url)
        time.sleep(5)
        
        logger.info(f"Page loaded: {driver.title}")
        
        # Get Aadhaar number
        root = tk.Tk()
        root.withdraw()
        aadhaar = simpledialog.askstring("Debug", "Enter Aadhaar number:", parent=root)
        root.destroy()
        
        if not aadhaar:
            return
        
        # Fill Aadhaar
        input_elements = driver.find_elements(By.TAG_NAME, "input")
        logger.info(f"Found {len(input_elements)} input elements")
        
        target_input = None
        for i, inp in enumerate(input_elements):
            try:
                input_type = inp.get_attribute("type") or ""
                input_class = inp.get_attribute("class") or ""
                is_displayed = inp.is_displayed()
                is_enabled = inp.is_enabled()
                
                logger.info(f"Input {i+1}: type={input_type}, class={input_class[:50]}, displayed={is_displayed}, enabled={is_enabled}")
                
                if is_displayed and is_enabled and input_type == "text":
                    target_input = inp
                    break
            except Exception as e:
                logger.warning(f"Error checking input {i+1}: {e}")
        
        if target_input:
            driver.execute_script("arguments[0].value = arguments[1];", target_input, aadhaar)
            logger.info("Aadhaar filled successfully")
            
            # Wait a moment for any dynamic changes
            time.sleep(3)
            
            # Now debug what's available
            logger.info("=== DEBUGGING AFTER AADHAAR FILLED ===")
            
            # Check all elements again
            all_elements = driver.find_elements(By.TAG_NAME, "*")
            logger.info(f"Total elements on page: {len(all_elements)}")
            
            # Look for images (potential captcha)
            images = driver.find_elements(By.TAG_NAME, "img")
            logger.info(f"Found {len(images)} image elements:")
            
            for i, img in enumerate(images):
                try:
                    src = img.get_attribute("src") or ""
                    img_class = img.get_attribute("class") or ""
                    is_displayed = img.is_displayed()
                    
                    logger.info(f"Image {i+1}: class={img_class}, displayed={is_displayed}")
                    logger.info(f"  Src: {src[:100]}{'...' if len(src) > 100 else ''}")
                    
                    if "data:image" in src:
                        logger.info(f"  *** FOUND BASE64 IMAGE ***")
                    
                except Exception as e:
                    logger.warning(f"Error checking image {i+1}: {e}")
            
            # Look for buttons
            buttons = driver.find_elements(By.TAG_NAME, "button")
            logger.info(f"Found {len(buttons)} button elements:")
            
            for i, btn in enumerate(buttons):
                try:
                    btn_text = btn.text or ""
                    btn_class = btn.get_attribute("class") or ""
                    btn_type = btn.get_attribute("type") or ""
                    is_displayed = btn.is_displayed()
                    is_enabled = btn.is_enabled()
                    
                    logger.info(f"Button {i+1}: text='{btn_text}', class={btn_class[:50]}, type={btn_type}, displayed={is_displayed}, enabled={is_enabled}")
                    
                except Exception as e:
                    logger.warning(f"Error checking button {i+1}: {e}")
            
            # Look for all input fields again (captcha might appear)
            inputs_after = driver.find_elements(By.TAG_NAME, "input")
            logger.info(f"Input fields after Aadhaar filled: {len(inputs_after)}")
            
            for i, inp in enumerate(inputs_after):
                try:
                    input_type = inp.get_attribute("type") or ""
                    input_class = inp.get_attribute("class") or ""
                    input_placeholder = inp.get_attribute("placeholder") or ""
                    input_value = inp.get_attribute("value") or ""
                    is_displayed = inp.is_displayed()
                    is_enabled = inp.is_enabled()
                    
                    logger.info(f"Input {i+1}: type={input_type}, class={input_class[:50]}")
                    logger.info(f"  placeholder='{input_placeholder}', value='{input_value[:20]}', displayed={is_displayed}, enabled={is_enabled}")
                    
                except Exception as e:
                    logger.warning(f"Error checking input {i+1}: {e}")
            
            # Look for divs that might contain captcha
            divs = driver.find_elements(By.TAG_NAME, "div")
            captcha_divs = []
            
            for div in divs:
                try:
                    div_class = div.get_attribute("class") or ""
                    if "captcha" in div_class.lower():
                        captcha_divs.append(div)
                except:
                    pass
            
            logger.info(f"Found {len(captcha_divs)} divs with 'captcha' in class name")
            
            for i, div in enumerate(captcha_divs):
                try:
                    div_class = div.get_attribute("class") or ""
                    is_displayed = div.is_displayed()
                    logger.info(f"Captcha div {i+1}: class={div_class}, displayed={is_displayed}")
                except Exception as e:
                    logger.warning(f"Error checking captcha div {i+1}: {e}")
            
            # Save page source for manual inspection
            with open("page_after_aadhaar.html", "w", encoding="utf-8") as f:
                f.write(driver.page_source)
            logger.info("Page source saved to page_after_aadhaar.html")
            
            logger.info("=== DEBUG COMPLETE ===")
            
            # Keep browser open for manual inspection
            input("Press Enter to close browser...")
        
    except Exception as e:
        logger.error(f"Error in debug: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_after_aadhaar()
