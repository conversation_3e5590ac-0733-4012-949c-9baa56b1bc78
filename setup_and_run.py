#!/usr/bin/env python3
"""
Setup and Run Script for Aadhaar Validator
Handles WebDriver installation and execution
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """Install required packages"""
    try:
        print("Installing required packages...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False

def install_webdriver():
    """Install ChromeDriver using webdriver-manager"""
    try:
        print("Setting up ChromeDriver...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "webdriver-manager"])
        
        # Create a script to download ChromeDriver
        webdriver_setup = """
from webdriver_manager.chrome import ChromeDriverManager
from selenium import webdriver
from selenium.webdriver.chrome.service import Service

try:
    # Download and install ChromeDriver
    driver_path = ChromeDriverManager().install()
    print(f"ChromeDriver installed at: {driver_path}")
    
    # Test the installation
    service = Service(driver_path)
    driver = webdriver.Chrome(service=service)
    driver.quit()
    print("✅ ChromeDriver setup successful")
except Exception as e:
    print(f"❌ ChromeDriver setup failed: {e}")
"""
        
        exec(webdriver_setup)
        return True
    except Exception as e:
        print(f"❌ Error setting up ChromeDriver: {e}")
        return False

def run_validator():
    """Run the Aadhaar validator"""
    try:
        print("\n🚀 Starting Aadhaar Validator...")
        from aadhaar_validator import AadhaarValidator
        
        validator = AadhaarValidator()
        success = validator.run()
        
        if success:
            print("✅ Aadhaar validation completed successfully")
        else:
            print("❌ Aadhaar validation failed")
            
        return success
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all requirements are installed")
        return False
    except Exception as e:
        print(f"❌ Error running validator: {e}")
        return False

def main():
    """Main setup and execution function"""
    print("🔧 Aadhaar Validator Setup and Runner")
    print("=" * 40)
    
    # Check if requirements.txt exists
    if not Path("requirements.txt").exists():
        print("❌ requirements.txt not found")
        return False
    
    # Install requirements
    if not install_requirements():
        return False
    
    # Setup WebDriver
    if not install_webdriver():
        return False
    
    # Run the validator
    return run_validator()

if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n❌ Setup or execution failed")
        input("Press Enter to exit...")
        sys.exit(1)
    else:
        print("\n✅ All operations completed successfully")
        input("Press Enter to exit...")
