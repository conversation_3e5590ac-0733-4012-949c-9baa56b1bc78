#!/usr/bin/env python3
"""
PERFECT Aadhaar Validator - Uses exact website structure discovered
"""

import time
import base64
import io
import tkinter as tk
from tkinter import messagebox, simpledialog
from PIL import Image, ImageTk
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PerfectAadhaarValidator:
    def __init__(self):
        self.driver = None
        self.target_url = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
        
    def setup_driver(self):
        """Setup WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            logger.info("WebDriver initialized")
            return True
        except Exception as e:
            logger.error(f"WebDriver setup failed: {e}")
            return False
    
    def step1_load_website(self):
        """Step 1: Load website FIRST"""
        try:
            logger.info("Step 1: Loading UIDAI website...")
            self.driver.get(self.target_url)
            time.sleep(5)
            logger.info(f"✅ Website loaded: {self.driver.title}")
            return True
        except Exception as e:
            logger.error(f"Step 1 failed: {e}")
            return False
    
    def step2_get_aadhaar(self):
        """Step 2: Get Aadhaar number AFTER website loads"""
        try:
            logger.info("Step 2: Getting Aadhaar number...")
            
            root = tk.Tk()
            root.withdraw()
            
            aadhaar = simpledialog.askstring(
                "Step 2: Aadhaar Input", 
                "✅ Website loaded successfully!\n\nEnter your 12-digit Aadhaar number:",
                parent=root
            )
            root.destroy()
            
            if not aadhaar:
                return None
                
            aadhaar = aadhaar.replace(" ", "").replace("-", "")
            if len(aadhaar) == 12 and aadhaar.isdigit():
                logger.info(f"✅ Got Aadhaar: {aadhaar}")
                return aadhaar
            else:
                messagebox.showerror("Invalid", "Please enter a valid 12-digit Aadhaar number")
                return None
                
        except Exception as e:
            logger.error(f"Step 2 failed: {e}")
            return None
    
    def step3_fill_aadhaar(self, aadhaar):
        """Step 3: Fill Aadhaar in first input field"""
        try:
            logger.info("Step 3: Filling Aadhaar number...")
            
            # Find inputs with the exact class we discovered
            inputs = self.driver.find_elements(By.CSS_SELECTOR, "input.sc-fBWQRz.bjSLyk")
            logger.info(f"Found {len(inputs)} input elements with target class")
            
            if len(inputs) < 1:
                logger.error("Could not find Aadhaar input field")
                return False
            
            # First input is for Aadhaar
            aadhaar_input = inputs[0]
            
            # Fill using JavaScript
            self.driver.execute_script("arguments[0].value = arguments[1];", aadhaar_input, aadhaar)
            
            # Verify
            current_value = aadhaar_input.get_attribute("value")
            if current_value == aadhaar:
                logger.info("✅ Aadhaar number filled successfully")
                return True
            else:
                logger.error(f"Fill failed. Expected: {aadhaar}, Got: {current_value}")
                return False
                
        except Exception as e:
            logger.error(f"Step 3 failed: {e}")
            return False
    
    def step4_extract_captcha(self):
        """Step 4: Extract captcha using exact class discovered"""
        try:
            logger.info("Step 4: Extracting captcha image...")
            
            # Wait a moment for captcha to be ready
            time.sleep(2)
            
            # Find captcha image using the exact class we discovered
            captcha_selectors = [
                "img.auth-form__captcha-image.check-aadhaar-validity__captcha-image",
                "img.auth-form__captcha-image",
                "img[class*='captcha-image']"
            ]
            
            for selector in captcha_selectors:
                try:
                    logger.info(f"Trying selector: {selector}")
                    captcha_img = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    if captcha_img.is_displayed():
                        src = captcha_img.get_attribute("src")
                        logger.info(f"Found captcha image with src type: {'base64' if 'data:' in src else 'URL'}")
                        
                        if "data:" in src and ("image" in src or "application/image" in src):
                            # Extract base64 data
                            base64_data = src.split(",")[1]
                            image_data = base64.b64decode(base64_data)
                            image = Image.open(io.BytesIO(image_data))
                            logger.info("✅ Successfully extracted captcha image")
                            return image
                            
                except Exception as e:
                    logger.info(f"Selector {selector} failed: {e}")
                    continue
            
            logger.error("Could not find captcha image")
            return None
            
        except Exception as e:
            logger.error(f"Step 4 failed: {e}")
            return None
    
    def step5_show_captcha_gui(self, captcha_image):
        """Step 5: Show captcha in GUI and get user input"""
        try:
            logger.info("Step 5: Showing captcha GUI...")
            
            root = tk.Tk()
            root.title("🔐 Solve Captcha - Step 5")
            root.geometry("500x400")
            root.eval('tk::PlaceWindow . center')
            
            # Title
            tk.Label(root, text="🔐 Step 5: Solve Captcha", 
                    font=("Arial", 16, "bold"), fg="#2E86AB").pack(pady=10)
            
            # Display image (resize for better visibility)
            display_image = captcha_image.resize((300, 150), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(display_image)
            
            img_frame = tk.Frame(root, relief="solid", bd=2)
            img_frame.pack(pady=10)
            img_label = tk.Label(img_frame, image=photo, bg="white")
            img_label.pack(padx=10, pady=10)
            
            # Input
            tk.Label(root, text="Enter the captcha text shown above:", 
                    font=("Arial", 12)).pack(pady=5)
            
            captcha_entry = tk.Entry(root, font=("Arial", 16), width=15, justify="center")
            captcha_entry.pack(pady=5)
            captcha_entry.focus()
            
            captcha_text = None
            
            def submit():
                nonlocal captcha_text
                text = captcha_entry.get().strip()
                if text:
                    captcha_text = text
                    root.destroy()
                else:
                    messagebox.showerror("Error", "Please enter the captcha text")
            
            def on_enter(event):
                submit()
            
            captcha_entry.bind('<Return>', on_enter)
            
            # Buttons
            button_frame = tk.Frame(root)
            button_frame.pack(pady=20)
            
            tk.Button(button_frame, text="✓ Submit", command=submit,
                     font=("Arial", 12, "bold"), bg="#28A745", fg="white",
                     padx=20, pady=5).pack(side=tk.LEFT, padx=10)
            
            tk.Button(button_frame, text="✗ Cancel", command=root.destroy,
                     font=("Arial", 12), bg="#DC3545", fg="white",
                     padx=20, pady=5).pack(side=tk.LEFT, padx=10)
            
            root.mainloop()
            
            if captcha_text:
                logger.info(f"✅ Got captcha solution: {captcha_text}")
            
            return captcha_text
            
        except Exception as e:
            logger.error(f"Step 5 failed: {e}")
            return None
    
    def step6_fill_captcha(self, captcha_text):
        """Step 6: Fill captcha in second input field"""
        try:
            logger.info("Step 6: Filling captcha in website...")
            
            # Find inputs with the exact class
            inputs = self.driver.find_elements(By.CSS_SELECTOR, "input.sc-fBWQRz.bjSLyk")
            
            if len(inputs) < 2:
                logger.error("Could not find captcha input field")
                return False
            
            # Second input is for captcha
            captcha_input = inputs[1]
            
            # Fill using JavaScript
            self.driver.execute_script("arguments[0].value = arguments[1];", captcha_input, captcha_text)
            
            # Verify
            current_value = captcha_input.get_attribute("value")
            if current_value == captcha_text:
                logger.info("✅ Captcha filled successfully")
                return True
            else:
                logger.error(f"Captcha fill failed. Expected: {captcha_text}, Got: {current_value}")
                return False
                
        except Exception as e:
            logger.error(f"Step 6 failed: {e}")
            return False
    
    def step7_click_proceed(self):
        """Step 7: Click Proceed button"""
        try:
            logger.info("Step 7: Clicking Proceed button...")
            
            # Find Proceed button using the exact class we discovered
            proceed_selectors = [
                "button.button_btn__HeAxz",
                "button[class*='button_btn']",
                "button:contains('Proceed')"
            ]
            
            for selector in proceed_selectors:
                try:
                    logger.info(f"Trying button selector: {selector}")
                    if "contains" in selector:
                        # Use XPath for text-based selection
                        button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Proceed')]")
                    else:
                        button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    if button.is_displayed() and button.is_enabled():
                        # Scroll to button
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                        time.sleep(1)
                        
                        # Click
                        button.click()
                        logger.info("✅ Proceed button clicked successfully")
                        return True
                        
                except Exception as e:
                    logger.info(f"Button selector {selector} failed: {e}")
                    continue
            
            logger.error("Could not find or click Proceed button")
            return False
            
        except Exception as e:
            logger.error(f"Step 7 failed: {e}")
            return False
    
    def step8_extract_results(self):
        """Step 8: Wait for results and extract them"""
        try:
            logger.info("Step 8: Waiting for results...")

            # Wait for page to process
            time.sleep(5)

            # Get both page source and visible text
            page_source = self.driver.page_source
            page_text = page_source.lower()

            results = {
                "status": "UNKNOWN",
                "details": [],
                "raw_data": {}
            }

            # Check for success indicators
            if any(word in page_text for word in ["exists", "valid", "found"]):
                results["status"] = "EXISTS"
                logger.info("✅ Aadhaar validation successful - EXISTS")
            elif any(word in page_text for word in ["not exist", "invalid", "not found"]):
                results["status"] = "NOT_EXISTS"
                logger.info("❌ Aadhaar validation - NOT EXISTS")

            # Save page source for debugging
            try:
                with open("results_page.html", "w", encoding="utf-8") as f:
                    f.write(page_source)
                logger.info("Results page saved to results_page.html")
            except:
                pass

            # Try multiple extraction methods
            logger.info("Extracting detailed information...")

            # Method 1: Look for specific text patterns
            try:
                import re

                # More flexible patterns for age band
                age_patterns = [
                    r'age\s*band[:\s]*(\d+-\d+\s*years?)',
                    r'age[:\s]*(\d+-\d+\s*years?)',
                    r'(\d+-\d+\s*years?)',
                    r'age\s*group[:\s]*(\d+-\d+)'
                ]

                for pattern in age_patterns:
                    age_match = re.search(pattern, page_text, re.IGNORECASE)
                    if age_match:
                        age_band = age_match.group(1)
                        results["details"].append(f"Age Band: {age_band}")
                        results["raw_data"]["age_band"] = age_band
                        logger.info(f"Found age band: {age_band}")
                        break

                # Gender patterns
                gender_patterns = [
                    r'gender[:\s]*(male|female)',
                    r'sex[:\s]*(male|female)',
                    r'(male|female)'
                ]

                for pattern in gender_patterns:
                    gender_match = re.search(pattern, page_text, re.IGNORECASE)
                    if gender_match:
                        gender = gender_match.group(1).upper()
                        results["details"].append(f"Gender: {gender}")
                        results["raw_data"]["gender"] = gender
                        logger.info(f"Found gender: {gender}")
                        break

                # State patterns
                state_patterns = [
                    r'state[:\s]*([a-zA-Z\s]{3,30})',
                    r'location[:\s]*([a-zA-Z\s]{3,30})',
                    r'address[:\s]*([a-zA-Z\s]{3,30})'
                ]

                for pattern in state_patterns:
                    state_match = re.search(pattern, page_text, re.IGNORECASE)
                    if state_match:
                        state = state_match.group(1).strip()
                        # Filter out common non-state words
                        if len(state) > 2 and state.lower() not in ['the', 'and', 'for', 'with']:
                            results["details"].append(f"State: {state}")
                            results["raw_data"]["state"] = state
                            logger.info(f"Found state: {state}")
                            break

                # Mobile patterns
                mobile_patterns = [
                    r'mobile[:\s]*(\*+\d+)',
                    r'phone[:\s]*(\*+\d+)',
                    r'(\*{3,}\d{3,})',
                    r'mobile[:\s]*(\*+.*?\d+)'
                ]

                for pattern in mobile_patterns:
                    mobile_match = re.search(pattern, page_text, re.IGNORECASE)
                    if mobile_match:
                        mobile = mobile_match.group(1)
                        results["details"].append(f"Mobile: {mobile}")
                        results["raw_data"]["mobile"] = mobile
                        logger.info(f"Found mobile: {mobile}")
                        break

            except Exception as e:
                logger.warning(f"Error in regex extraction: {e}")

            # Method 2: Look for structured data in DOM elements
            try:
                logger.info("Trying DOM element extraction...")

                # Look for table rows or list items that might contain the data
                elements_to_check = [
                    self.driver.find_elements(By.TAG_NAME, "tr"),
                    self.driver.find_elements(By.TAG_NAME, "li"),
                    self.driver.find_elements(By.TAG_NAME, "div"),
                    self.driver.find_elements(By.TAG_NAME, "span"),
                    self.driver.find_elements(By.TAG_NAME, "p")
                ]

                for element_list in elements_to_check:
                    for element in element_list:
                        try:
                            text = element.text.strip().lower()
                            if text and any(keyword in text for keyword in ['age', 'gender', 'state', 'mobile']):
                                logger.info(f"Found potential data element: {text[:100]}")

                                # Try to extract from this element
                                if 'age' in text and any(char.isdigit() for char in text):
                                    age_match = re.search(r'(\d+-\d+\s*years?)', text)
                                    if age_match and "age_band" not in results["raw_data"]:
                                        age_band = age_match.group(1)
                                        results["details"].append(f"Age Band: {age_band}")
                                        results["raw_data"]["age_band"] = age_band
                                        logger.info(f"DOM extracted age band: {age_band}")

                                if 'gender' in text and ('male' in text or 'female' in text):
                                    gender = 'MALE' if 'male' in text else 'FEMALE'
                                    if "gender" not in results["raw_data"]:
                                        results["details"].append(f"Gender: {gender}")
                                        results["raw_data"]["gender"] = gender
                                        logger.info(f"DOM extracted gender: {gender}")

                                if 'mobile' in text and '*' in text:
                                    mobile_match = re.search(r'(\*+\d+)', text)
                                    if mobile_match and "mobile" not in results["raw_data"]:
                                        mobile = mobile_match.group(1)
                                        results["details"].append(f"Mobile: {mobile}")
                                        results["raw_data"]["mobile"] = mobile
                                        logger.info(f"DOM extracted mobile: {mobile}")

                        except Exception as e:
                            continue

            except Exception as e:
                logger.warning(f"Error in DOM extraction: {e}")

            # Method 3: Look for JSON data in script tags
            try:
                logger.info("Checking for JSON data...")
                script_elements = self.driver.find_elements(By.TAG_NAME, "script")

                for script in script_elements:
                    try:
                        script_content = script.get_attribute("innerHTML") or ""
                        if script_content and any(keyword in script_content.lower() for keyword in ['age', 'gender', 'state', 'mobile']):
                            logger.info("Found potential JSON data in script tag")
                            # Try to extract JSON data here if needed
                    except:
                        continue

            except Exception as e:
                logger.warning(f"Error checking JSON data: {e}")

            logger.info(f"Extraction complete. Found {len(results['details'])} details")
            return results

        except Exception as e:
            logger.error(f"Step 8 failed: {e}")
            return {"status": "ERROR", "error": str(e)}
    
    def step9_display_results(self, results):
        """Step 9: Display final results"""
        try:
            logger.info("Step 9: Displaying results...")
            
            root = tk.Tk()
            root.title("🎯 Aadhaar Validation Results")
            root.geometry("500x400")
            root.eval('tk::PlaceWindow . center')
            
            # Title
            tk.Label(root, text="🎯 Aadhaar Validation Complete!", 
                    font=("Arial", 18, "bold"), fg="#2E86AB").pack(pady=20)
            
            # Status
            status = results.get("status", "UNKNOWN")
            if status == "EXISTS":
                status_text = "✅ Aadhaar Number EXISTS"
                status_color = "#28A745"
            elif status == "NOT_EXISTS":
                status_text = "❌ Aadhaar Number NOT EXISTS"
                status_color = "#DC3545"
            else:
                status_text = f"❓ Status: {status}"
                status_color = "#FFC107"
            
            tk.Label(root, text=status_text, 
                    font=("Arial", 16, "bold"), fg=status_color).pack(pady=10)
            
            # Details
            if results.get("details"):
                details_frame = tk.LabelFrame(root, text="Details", 
                                            font=("Arial", 12, "bold"), padx=20, pady=10)
                details_frame.pack(pady=20, padx=20, fill="x")
                
                for detail in results["details"]:
                    tk.Label(details_frame, text=detail, 
                            font=("Arial", 12), anchor="w").pack(fill="x", pady=2)
            
            # Close button
            tk.Button(root, text="Close", command=root.destroy,
                     font=("Arial", 12, "bold"), bg="#6C757D", fg="white",
                     padx=30, pady=10).pack(pady=20)
            
            root.mainloop()
            
        except Exception as e:
            logger.error(f"Step 9 failed: {e}")
            messagebox.showinfo("Results", f"Validation Status: {results.get('status', 'Unknown')}")
    
    def run_perfect(self):
        """Run perfect validation with exact website structure"""
        try:
            logger.info("🚀 Starting PERFECT Aadhaar Validator")
            
            # Setup
            if not self.setup_driver():
                messagebox.showerror("Error", "Failed to setup browser")
                return
            
            # Step 1: Load website FIRST
            if not self.step1_load_website():
                return
            
            # Step 2: Get Aadhaar AFTER website loads
            aadhaar = self.step2_get_aadhaar()
            if not aadhaar:
                return
            
            # Step 3: Fill Aadhaar
            if not self.step3_fill_aadhaar(aadhaar):
                return
            
            # Step 4: Extract captcha
            captcha_image = self.step4_extract_captcha()
            if not captcha_image:
                messagebox.showerror("Error", "Could not extract captcha image")
                return
            
            # Step 5: Show captcha GUI
            captcha_text = self.step5_show_captcha_gui(captcha_image)
            if not captcha_text:
                return
            
            # Step 6: Fill captcha
            if not self.step6_fill_captcha(captcha_text):
                return
            
            # Step 7: Click Proceed
            if not self.step7_click_proceed():
                return
            
            # Step 8: Extract results
            results = self.step8_extract_results()
            
            # Step 9: Display results
            self.step9_display_results(results)
            
            logger.info("🎉 Perfect validation completed!")
            
        except Exception as e:
            logger.error(f"Perfect validation failed: {e}")
            messagebox.showerror("Error", f"An error occurred: {e}")
        finally:
            if self.driver:
                self.driver.quit()
                logger.info("Browser closed")

if __name__ == "__main__":
    validator = PerfectAadhaarValidator()
    validator.run_perfect()
