#!/usr/bin/env python3
"""
Website Inspector - Check the actual structure of the UIDAI website
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import time

def inspect_website():
    """Inspect the UIDAI website structure"""
    
    # Setup Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    # Initialize driver
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    wait = WebDriverWait(driver, 10)
    
    try:
        print("🔍 Inspecting UIDAI website structure...")
        
        # Navigate to the website
        url = "https://myaadhaar.uidai.gov.in/check-aadhaar-validity/en"
        print(f"Navigating to: {url}")
        driver.get(url)
        
        # Wait for page to load
        time.sleep(5)
        
        print(f"Page title: {driver.title}")
        print(f"Current URL: {driver.current_url}")
        print()
        
        # Find all input elements
        print("=== INPUT ELEMENTS ===")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"Found {len(inputs)} input elements:")
        
        for i, inp in enumerate(inputs):
            try:
                input_type = inp.get_attribute("type") or "unknown"
                input_class = inp.get_attribute("class") or "no-class"
                input_id = inp.get_attribute("id") or "no-id"
                input_name = inp.get_attribute("name") or "no-name"
                input_placeholder = inp.get_attribute("placeholder") or "no-placeholder"
                is_enabled = inp.is_enabled()
                is_displayed = inp.is_displayed()
                
                print(f"Input {i+1}:")
                print(f"  Type: {input_type}")
                print(f"  Class: {input_class}")
                print(f"  ID: {input_id}")
                print(f"  Name: {input_name}")
                print(f"  Placeholder: {input_placeholder}")
                print(f"  Enabled: {is_enabled}")
                print(f"  Displayed: {is_displayed}")
                print()
                
            except Exception as e:
                print(f"Input {i+1}: Error - {e}")
                print()
        
        # Find all buttons
        print("=== BUTTON ELEMENTS ===")
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"Found {len(buttons)} button elements:")
        
        for i, btn in enumerate(buttons):
            try:
                btn_text = btn.text or "no-text"
                btn_class = btn.get_attribute("class") or "no-class"
                btn_id = btn.get_attribute("id") or "no-id"
                btn_type = btn.get_attribute("type") or "unknown"
                is_enabled = btn.is_enabled()
                is_displayed = btn.is_displayed()
                
                print(f"Button {i+1}:")
                print(f"  Text: {btn_text}")
                print(f"  Class: {btn_class}")
                print(f"  ID: {btn_id}")
                print(f"  Type: {btn_type}")
                print(f"  Enabled: {is_enabled}")
                print(f"  Displayed: {is_displayed}")
                print()
                
            except Exception as e:
                print(f"Button {i+1}: Error - {e}")
                print()
        
        # Find all images (for captcha)
        print("=== IMAGE ELEMENTS ===")
        images = driver.find_elements(By.TAG_NAME, "img")
        print(f"Found {len(images)} image elements:")
        
        for i, img in enumerate(images):
            try:
                img_src = img.get_attribute("src") or "no-src"
                img_class = img.get_attribute("class") or "no-class"
                img_id = img.get_attribute("id") or "no-id"
                img_alt = img.get_attribute("alt") or "no-alt"
                is_displayed = img.is_displayed()
                
                print(f"Image {i+1}:")
                print(f"  Src: {img_src[:100]}{'...' if len(img_src) > 100 else ''}")
                print(f"  Class: {img_class}")
                print(f"  ID: {img_id}")
                print(f"  Alt: {img_alt}")
                print(f"  Displayed: {is_displayed}")
                print()
                
            except Exception as e:
                print(f"Image {i+1}: Error - {e}")
                print()
        
        # Find all forms
        print("=== FORM ELEMENTS ===")
        forms = driver.find_elements(By.TAG_NAME, "form")
        print(f"Found {len(forms)} form elements:")
        
        for i, form in enumerate(forms):
            try:
                form_class = form.get_attribute("class") or "no-class"
                form_id = form.get_attribute("id") or "no-id"
                form_action = form.get_attribute("action") or "no-action"
                form_method = form.get_attribute("method") or "no-method"
                
                print(f"Form {i+1}:")
                print(f"  Class: {form_class}")
                print(f"  ID: {form_id}")
                print(f"  Action: {form_action}")
                print(f"  Method: {form_method}")
                print()
                
            except Exception as e:
                print(f"Form {i+1}: Error - {e}")
                print()
        
        # Look for specific classes mentioned in requirements
        print("=== CHECKING SPECIFIC CLASSES ===")
        
        classes_to_check = [
            "auth-form__text-field",
            "auth-form__captcha-field", 
            "auth-form__captcha-box",
            "auth-form__button-container",
            "auth-form__button"
        ]
        
        for class_name in classes_to_check:
            try:
                elements = driver.find_elements(By.CLASS_NAME, class_name)
                print(f"Class '{class_name}': Found {len(elements)} elements")
                
                for i, elem in enumerate(elements):
                    try:
                        tag_name = elem.tag_name
                        is_enabled = elem.is_enabled()
                        is_displayed = elem.is_displayed()
                        text = elem.text[:50] if elem.text else "no-text"
                        
                        print(f"  Element {i+1}: <{tag_name}> enabled={is_enabled} displayed={is_displayed} text='{text}'")
                    except Exception as e:
                        print(f"  Element {i+1}: Error - {e}")
                        
            except Exception as e:
                print(f"Class '{class_name}': Error - {e}")
            print()
        
        print("✅ Website inspection completed!")
        
    except Exception as e:
        print(f"❌ Error during inspection: {e}")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    inspect_website()
